import React, { useState, useEffect, useRef, useCallback } from 'react';
import { MapPin, Layers, Satellite, Navigation, Filter, Crosshair, AlertCircle, Camera, RefreshCw, Target, RotateCcw } from 'lucide-react';
import LocationInfo from './LocationInfo';

// Interface para imagens de drone
interface DroneImage {
  id: number;
  filename: string;
  latitude: number;
  longitude: number;
  altitude: number;
  capture_date: string;
  ndvi_data?: {
    mean: number;
    min: number;
    max: number;
  };
  analysis_results?: {
    vegetation_health: 'healthy' | 'stressed' | 'critical';
    pest_detection: boolean;
    disease_detection: boolean;
  };
}

const MapViewSimple: React.FC = () => {
  const [selectedLayer, setSelectedLayer] = useState('hybrid');
  const [selectedFarm, setSelectedFarm] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([-12.5489, -55.7183]);
  const [mapZoom, setMapZoom] = useState(10);
  const [droneImages, setDroneImages] = useState<DroneImage[]>([]);
  const [selectedDroneImage, setSelectedDroneImage] = useState<number | null>(null);
  const [showDroneOverlays, setShowDroneOverlays] = useState(true);
  const [isTrackingLocation, setIsTrackingLocation] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<google.maps.Map | null>(null);
  const markersRef = useRef<google.maps.Marker[]>([]);

  const farms = [
    {
      id: 1,
      name: 'Fazenda São João',
      coordinates: { lat: -12.5489, lng: -55.7183 },
      area: 2450,
      status: 'healthy',
      ndvi: 0.75,
      alerts: 1,
      description: 'Propriedade com cultivo de soja e milho',
      owner: 'João Silva'
    },
    {
      id: 2,
      name: 'Fazenda Esperança',
      coordinates: { lat: -15.5561, lng: -54.2961 },
      area: 1890,
      status: 'warning',
      ndvi: 0.62,
      alerts: 3,
      description: 'Fazenda especializada em algodão',
      owner: 'Maria Santos'
    },
    {
      id: 3,
      name: 'Fazenda Progresso',
      coordinates: { lat: -13.2500, lng: -56.1000 },
      area: 3200,
      status: 'healthy',
      ndvi: 0.82,
      alerts: 0,
      description: 'Grande propriedade com cultivos diversificados',
      owner: 'Carlos Oliveira'
    }
  ];

  // Dados simulados de imagens de drone
  const mockDroneImages: DroneImage[] = [
    {
      id: 1,
      filename: 'drone_001_rgb.jpg',
      latitude: -12.5489,
      longitude: -55.7183,
      altitude: 120,
      capture_date: '2024-06-20T10:30:00',
      ndvi_data: {
        mean: 0.75,
        min: 0.45,
        max: 0.89
      },
      analysis_results: {
        vegetation_health: 'healthy',
        pest_detection: false,
        disease_detection: false
      }
    },
    {
      id: 2,
      filename: 'drone_002_nir.jpg',
      latitude: -12.5495,
      longitude: -55.7190,
      altitude: 100,
      capture_date: '2024-06-21T09:15:00',
      ndvi_data: {
        mean: 0.62,
        min: 0.38,
        max: 0.78
      },
      analysis_results: {
        vegetation_health: 'stressed',
        pest_detection: true,
        disease_detection: false
      }
    },
    {
      id: 3,
      filename: 'drone_003_multi.jpg',
      latitude: -13.2500,
      longitude: -56.1000,
      altitude: 150,
      capture_date: '2024-06-22T14:45:00',
      ndvi_data: {
        mean: 0.82,
        min: 0.65,
        max: 0.95
      },
      analysis_results: {
        vegetation_health: 'healthy',
        pest_detection: false,
        disease_detection: false
      }
    }
  ];

  // Carregar imagens de drone
  useEffect(() => {
    setDroneImages(mockDroneImages);
  }, []);

  // Função para obter localização GPS
  const getCurrentLocation = useCallback(() => {
    setLocationError(null);

    if (!navigator.geolocation) {
      setLocationError('Geolocalização não é suportada neste navegador');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        const newLocation: [number, number] = [latitude, longitude];
        
        console.log('Localização obtida:', {
          latitude,
          longitude,
          accuracy: `${accuracy}m`,
          timestamp: new Date(position.timestamp).toLocaleString()
        });
        
        setUserLocation(newLocation);
        setMapCenter(newLocation);
        setMapZoom(16);
        
        // Atualizar mapa se estiver carregado
        if (googleMapRef.current) {
          googleMapRef.current.setCenter({ lat: latitude, lng: longitude });
          googleMapRef.current.setZoom(16);
        }
        
        setLocationError(`Localização obtida com precisão de ${accuracy.toFixed(0)}m`);
        setTimeout(() => setLocationError(null), 3000);
      },
      (error) => {
        console.error('Erro de geolocalização:', error);
        let errorMessage = 'Erro ao obter localização';
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Permissão de localização negada. Permita o acesso à localização.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Localização indisponível. Verifique se o GPS está ativado.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Timeout ao obter localização.';
            break;
        }
        setLocationError(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 30000
      }
    );
  }, []);

  // Função para calcular distância entre dois pontos
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Raio da Terra em km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Função para sincronizar com imagem de drone mais próxima
  const syncWithNearestDroneImage = () => {
    if (!userLocation || droneImages.length === 0) return;

    let nearestImage = droneImages[0];
    let minDistance = calculateDistance(
      userLocation[0], userLocation[1],
      nearestImage.latitude, nearestImage.longitude
    );

    droneImages.forEach(image => {
      const distance = calculateDistance(
        userLocation[0], userLocation[1],
        image.latitude, image.longitude
      );
      if (distance < minDistance) {
        minDistance = distance;
        nearestImage = image;
      }
    });

    setMapCenter([nearestImage.latitude, nearestImage.longitude]);
    setMapZoom(18);
    setSelectedDroneImage(nearestImage.id);
    
    if (googleMapRef.current) {
      googleMapRef.current.setCenter({ lat: nearestImage.latitude, lng: nearestImage.longitude });
      googleMapRef.current.setZoom(18);
    }
  };

  // Inicializar mapa quando o componente carregar
  useEffect(() => {
    const initMap = () => {
      if (!mapRef.current || !window.google) return;

      const map = new google.maps.Map(mapRef.current, {
        center: { lat: mapCenter[0], lng: mapCenter[1] },
        zoom: mapZoom,
        mapTypeId: selectedLayer === 'satellite' ? google.maps.MapTypeId.SATELLITE : 
                   selectedLayer === 'hybrid' ? google.maps.MapTypeId.HYBRID :
                   selectedLayer === 'terrain' ? google.maps.MapTypeId.TERRAIN :
                   google.maps.MapTypeId.ROADMAP,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
      });

      googleMapRef.current = map;

      // Adicionar marcadores das fazendas
      farms.forEach(farm => {
        const marker = new google.maps.Marker({
          position: { lat: farm.coordinates.lat, lng: farm.coordinates.lng },
          map: map,
          title: farm.name,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            scale: 8,
            fillColor: farm.status === 'healthy' ? '#10b981' : 
                      farm.status === 'warning' ? '#f59e0b' : '#ef4444',
            fillOpacity: 1,
            strokeColor: '#ffffff',
            strokeWeight: 2,
          }
        });

        const infoWindow = new google.maps.InfoWindow({
          content: `
            <div style="padding: 10px; min-width: 200px;">
              <h3 style="margin: 0 0 10px 0; font-weight: bold;">${farm.name}</h3>
              <p style="margin: 0 0 5px 0; font-size: 14px; color: #666;">${farm.description}</p>
              <div style="font-size: 12px; line-height: 1.4;">
                <div><strong>Proprietário:</strong> ${farm.owner}</div>
                <div><strong>Área:</strong> ${farm.area} ha</div>
                <div><strong>NDVI:</strong> ${farm.ndvi}</div>
                <div><strong>Status:</strong> ${farm.status === 'healthy' ? 'Saudável' : 
                                                farm.status === 'warning' ? 'Atenção' : 'Crítico'}</div>
                ${farm.alerts > 0 ? `<div style="color: #f59e0b;"><strong>Alertas:</strong> ${farm.alerts}</div>` : ''}
              </div>
            </div>
          `
        });

        marker.addListener('click', () => {
          infoWindow.open(map, marker);
          setSelectedFarm(farm.id);
        });

        markersRef.current.push(marker);
      });

      // Adicionar marcadores das imagens de drone
      droneImages.forEach(image => {
        const marker = new google.maps.Marker({
          position: { lat: image.latitude, lng: image.longitude },
          map: map,
          title: image.filename,
          icon: {
            path: google.maps.SymbolPath.BACKWARD_CLOSED_ARROW,
            scale: 6,
            fillColor: '#8b5cf6',
            fillOpacity: 1,
            strokeColor: '#ffffff',
            strokeWeight: 2,
          }
        });

        const infoWindow = new google.maps.InfoWindow({
          content: `
            <div style="padding: 10px; min-width: 250px;">
              <h3 style="margin: 0 0 10px 0; font-weight: bold;">📷 ${image.filename}</h3>
              <div style="font-size: 12px; line-height: 1.4;">
                <div><strong>Data:</strong> ${new Date(image.capture_date).toLocaleString('pt-BR')}</div>
                <div><strong>Altitude:</strong> ${image.altitude}m</div>
                <div><strong>Coordenadas:</strong> ${image.latitude.toFixed(6)}, ${image.longitude.toFixed(6)}</div>
                ${image.ndvi_data ? `
                  <div style="background: #f0fdf4; padding: 8px; margin: 8px 0; border-radius: 4px;">
                    <div><strong>NDVI Médio:</strong> ${image.ndvi_data.mean.toFixed(2)}</div>
                    <div><strong>Faixa:</strong> ${image.ndvi_data.min.toFixed(2)} - ${image.ndvi_data.max.toFixed(2)}</div>
                  </div>
                ` : ''}
                ${image.analysis_results ? `
                  <div style="background: #eff6ff; padding: 8px; margin: 8px 0; border-radius: 4px;">
                    <div><strong>Saúde:</strong> ${
                      image.analysis_results.vegetation_health === 'healthy' ? '🟢 Saudável' :
                      image.analysis_results.vegetation_health === 'stressed' ? '🟡 Estressada' : '🔴 Crítica'
                    }</div>
                    <div><strong>Pragas:</strong> ${image.analysis_results.pest_detection ? '⚠️ Detectadas' : '✅ Não detectadas'}</div>
                    <div><strong>Doenças:</strong> ${image.analysis_results.disease_detection ? '⚠️ Detectadas' : '✅ Não detectadas'}</div>
                  </div>
                ` : ''}
              </div>
            </div>
          `
        });

        marker.addListener('click', () => {
          infoWindow.open(map, marker);
          setSelectedDroneImage(image.id);
        });

        markersRef.current.push(marker);
      });

      // Adicionar marcador da localização do usuário se disponível
      if (userLocation) {
        const userMarker = new google.maps.Marker({
          position: { lat: userLocation[0], lng: userLocation[1] },
          map: map,
          title: 'Sua localização',
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            scale: 6,
            fillColor: '#3b82f6',
            fillOpacity: 1,
            strokeColor: '#ffffff',
            strokeWeight: 2,
          }
        });

        const userInfoWindow = new google.maps.InfoWindow({
          content: `
            <div style="padding: 10px;">
              <h3 style="margin: 0 0 10px 0; font-weight: bold;">📍 Sua localização atual</h3>
              <div style="font-size: 12px;">
                ${userLocation[0].toFixed(6)}, ${userLocation[1].toFixed(6)}
              </div>
            </div>
          `
        });

        userMarker.addListener('click', () => {
          userInfoWindow.open(map, userMarker);
        });

        markersRef.current.push(userMarker);
      }
    };

    // Carregar Google Maps API se não estiver carregada
    if (!window.google) {
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dO_BcqCGUOdAto&libraries=places`;
      script.async = true;
      script.defer = true;
      script.onload = initMap;
      document.head.appendChild(script);
    } else {
      initMap();
    }

    return () => {
      // Limpar marcadores
      markersRef.current.forEach(marker => marker.setMap(null));
      markersRef.current = [];
    };
  }, [mapCenter, mapZoom, selectedLayer, userLocation, droneImages]);

  // Obter localização automaticamente ao carregar
  useEffect(() => {
    if (navigator.geolocation) {
      const timer = setTimeout(() => {
        getCurrentLocation();
      }, 1000);
      
      return () => clearTimeout(timer);
    } else {
      setLocationError('Geolocalização não é suportada neste navegador');
    }
  }, [getCurrentLocation]);

  // Limpar watch quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [watchId]);

  const layers = [
    { id: 'hybrid', name: 'Híbrido', icon: Layers },
    { id: 'satellite', name: 'Satélite', icon: Satellite },
    { id: 'roadmap', name: 'Ruas', icon: MapPin },
    { id: 'terrain', name: 'Terreno', icon: Target },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'critical': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'healthy': return 'Saudável';
      case 'warning': return 'Atenção';
      case 'critical': return 'Crítico';
      default: return 'Desconhecido';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mapa Interativo (Google Maps)</h1>
          <p className="text-gray-600">Visualização geoespacial avançada com GPS e sincronização de imagens de drone</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Map Container */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Map Controls */}
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-lg font-semibold text-gray-900">Visualização</h3>
                  <div className="flex items-center space-x-2">
                    {layers.map((layer) => {
                      const Icon = layer.icon;
                      return (
                        <button
                          key={layer.id}
                          onClick={() => {
                            setSelectedLayer(layer.id);
                            if (googleMapRef.current) {
                              const mapTypeId = layer.id === 'satellite' ? google.maps.MapTypeId.SATELLITE :
                                              layer.id === 'hybrid' ? google.maps.MapTypeId.HYBRID :
                                              layer.id === 'terrain' ? google.maps.MapTypeId.TERRAIN :
                                              google.maps.MapTypeId.ROADMAP;
                              googleMapRef.current.setMapTypeId(mapTypeId);
                            }
                          }}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            selectedLayer === layer.id
                              ? 'bg-emerald-100 text-emerald-700'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span>{layer.name}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowDroneOverlays(!showDroneOverlays)}
                    className={`p-2 rounded-lg transition-colors ${
                      showDroneOverlays
                        ? 'text-purple-600 bg-purple-100 hover:bg-purple-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Mostrar/Ocultar imagens de drone"
                  >
                    <Camera className="w-5 h-5" />
                  </button>
                  <button
                    onClick={syncWithNearestDroneImage}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Sincronizar com imagem de drone mais próxima"
                  >
                    <RefreshCw className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Filtros"
                  >
                    <Filter className="w-5 h-5" />
                  </button>
                  <button
                    onClick={getCurrentLocation}
                    className={`p-2 rounded-lg transition-colors ${
                      userLocation
                        ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Minha localização"
                  >
                    <Navigation className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => {
                      setMapCenter([-12.5489, -55.7183]);
                      setMapZoom(10);
                      if (googleMapRef.current) {
                        googleMapRef.current.setCenter({ lat: -12.5489, lng: -55.7183 });
                        googleMapRef.current.setZoom(10);
                      }
                    }}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Centralizar no Brasil"
                  >
                    <RotateCcw className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Mapa Google Maps */}
              <div className="relative h-96">
                {locationError && (
                  <div className={`absolute top-2 left-2 z-10 px-3 py-2 rounded-lg text-sm flex items-center space-x-2 ${
                    locationError.includes('obtida')
                      ? 'bg-green-100 border border-green-400 text-green-700'
                      : 'bg-red-100 border border-red-400 text-red-700'
                  }`}>
                    <AlertCircle className="w-4 h-4" />
                    <span>{locationError}</span>
                  </div>
                )}

                {/* Debug Info */}
                {userLocation && (
                  <div className="absolute top-2 right-2 z-10 bg-blue-100 border border-blue-400 text-blue-700 px-3 py-2 rounded-lg text-xs">
                    <div><strong>GPS:</strong> {userLocation[0].toFixed(6)}, {userLocation[1].toFixed(6)}</div>
                    <div><strong>Zoom:</strong> {mapZoom}</div>
                    {isTrackingLocation && <div className="text-green-600"><strong>🎯 Rastreando</strong></div>}
                  </div>
                )}

                {/* Container do Mapa */}
                <div
                  ref={mapRef}
                  className="w-full h-full"
                  style={{ minHeight: '400px' }}
                />

                {/* Botão de Localização */}
                <button
                  onClick={getCurrentLocation}
                  className="absolute bottom-4 right-4 z-10 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                  title="Obter minha localização"
                >
                  <Crosshair className="w-5 h-5" />
                </button>

                {/* Legend */}
                <div className="absolute bottom-4 left-4 z-10 bg-white rounded-lg shadow-lg p-3">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Legenda</h4>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-green-500" />
                      <span className="text-xs text-gray-600">Fazenda Saudável</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-yellow-500" />
                      <span className="text-xs text-gray-600">Fazenda Atenção</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500" />
                      <span className="text-xs text-gray-600">Fazenda Crítica</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                      <span className="text-xs text-gray-600">Sua localização</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded bg-purple-500" />
                      <span className="text-xs text-gray-600">Imagem de drone</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Filters Panel */}
              {showFilters && (
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Filtros</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Status da Vegetação</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="healthy">Saudável</option>
                        <option value="warning">Atenção</option>
                        <option value="critical">Crítico</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Faixa NDVI</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todas</option>
                        <option value="high">Alto (0.7-1.0)</option>
                        <option value="medium">Médio (0.4-0.7)</option>
                        <option value="low">Baixo (0.0-0.4)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Tipo de Cultivo</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="soja">Soja</option>
                        <option value="milho">Milho</option>
                        <option value="algodao">Algodão</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Location Info */}
            <LocationInfo
              position={userLocation}
              onLocationUpdate={(data) => {
                setUserLocation([data.latitude, data.longitude]);
                setMapCenter([data.latitude, data.longitude]);
                if (googleMapRef.current) {
                  googleMapRef.current.setCenter({ lat: data.latitude, lng: data.longitude });
                }
              }}
            />

            {/* Drone Images List */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Imagens de Drone</h3>
                <button
                  onClick={() => setShowDroneOverlays(!showDroneOverlays)}
                  className={`text-xs px-2 py-1 rounded-full ${
                    showDroneOverlays
                      ? 'bg-purple-100 text-purple-700'
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {showDroneOverlays ? 'Visível' : 'Oculto'}
                </button>
              </div>
              <div className="space-y-3">
                {droneImages.map((image) => (
                  <div
                    key={image.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedDroneImage === image.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedDroneImage(selectedDroneImage === image.id ? null : image.id);
                      setMapCenter([image.latitude, image.longitude]);
                      setMapZoom(18);
                      if (googleMapRef.current) {
                        googleMapRef.current.setCenter({ lat: image.latitude, lng: image.longitude });
                        googleMapRef.current.setZoom(18);
                      }
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 text-sm">📷 {image.filename}</h4>
                      <span className="text-xs text-gray-500">
                        {new Date(image.capture_date).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p>Altitude: {image.altitude}m</p>
                      {image.ndvi_data && (
                        <p>NDVI: {image.ndvi_data.mean.toFixed(2)}</p>
                      )}
                      {image.analysis_results && (
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${
                            image.analysis_results.vegetation_health === 'healthy' ? 'bg-green-500' :
                            image.analysis_results.vegetation_health === 'stressed' ? 'bg-yellow-500' : 'bg-red-500'
                          }`} />
                          <span>{
                            image.analysis_results.vegetation_health === 'healthy' ? 'Saudável' :
                            image.analysis_results.vegetation_health === 'stressed' ? 'Estressada' : 'Crítica'
                          }</span>
                        </div>
                      )}
                      {userLocation && (
                        <p className="text-blue-600">
                          {calculateDistance(
                            userLocation[0], userLocation[1],
                            image.latitude, image.longitude
                          ).toFixed(1)} km
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Farm List */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Propriedades</h3>
              <div className="space-y-3">
                {farms.map((farm) => (
                  <div
                    key={farm.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedFarm === farm.id
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedFarm(selectedFarm === farm.id ? null : farm.id);
                      setMapCenter([farm.coordinates.lat, farm.coordinates.lng]);
                      setMapZoom(14);
                      if (googleMapRef.current) {
                        googleMapRef.current.setCenter({ lat: farm.coordinates.lat, lng: farm.coordinates.lng });
                        googleMapRef.current.setZoom(14);
                      }
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{farm.name}</h4>
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: getStatusColor(farm.status) }}
                      />
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Área: {farm.area} ha</p>
                      <p>NDVI: {farm.ndvi}</p>
                      <p>Status: {getStatusLabel(farm.status)}</p>
                      {farm.alerts > 0 && (
                        <p className="text-yellow-600">{farm.alerts} alerta(s)</p>
                      )}
                      {userLocation && (
                        <p className="text-blue-600">
                          {calculateDistance(
                            userLocation[0], userLocation[1],
                            farm.coordinates.lat, farm.coordinates.lng
                          ).toFixed(1)} km
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapViewSimple;
