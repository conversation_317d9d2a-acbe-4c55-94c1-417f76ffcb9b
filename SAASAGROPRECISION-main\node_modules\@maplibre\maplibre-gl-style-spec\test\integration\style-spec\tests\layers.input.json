{"version": 8, "sources": {"vector": {"type": "vector", "url": "https://demotiles.maplibre.org/tiles/tiles.json"}, "raster": {"type": "raster", "url": "https://demotiles.maplibre.org/terrain-tiles/tiles.json"}, "raster-dem": {"type": "raster-dem", "url": "https://demotiles.maplibre.org/terrain-tiles/tiles.json"}, "geojson": {"type": "g<PERSON><PERSON><PERSON>", "data": {}}, "lineMetrics": {"type": "g<PERSON><PERSON><PERSON>", "data": {}, "lineMetrics": true}}, "layers": [{"id": "no-ref-or-type", "source": "vector", "source-layer": "layer"}, {"type": "line", "source": "vector", "source-layer": "source-layer"}, {"id": "ref", "type": "line", "source": "vector", "source-layer": "source-layer"}, {"id": "ref-prohibited-properties", "ref": "ref", "type": "line", "source": "vector", "source-layer": "source-layer", "filter": ["any"], "layout": {}}, {"id": "ref-not-found", "ref": "not-found"}, {"id": "self-ref", "ref": "self-ref"}, {"id": "missing-source", "type": "line"}, {"id": "source-not-found", "type": "line", "source": "not-found"}, {"id": "vector-raster-mismatch", "type": "line", "source": "raster"}, {"id": "raster-vector-mismatch", "type": "raster", "source": "vector", "source-layer": "source-layer"}, {"id": "raster-hillshade-mismatch", "type": "hillshade", "source": "raster", "source-layer": "source-layer"}, {"id": "vector-hillshade-mismatch", "type": "hillshade", "source": "vector", "source-layer": "source-layer"}, {"id": "raster-dem-raster-mismatch", "type": "raster", "source": "raster-dem", "source-layer": "source-layer"}, {"id": "duplicate", "type": "line", "source": "vector", "source-layer": "source-layer"}, {"id": "duplicate", "type": "line", "source": "vector", "source-layer": "source-layer"}, {"id": "invalid-type", "type": "invalid", "source": "vector", "source-layer": "source-layer", "layout": {"invalid-size": 42}, "paint": {"invalid-color": "red"}}, {"id": "missing-source-layer", "type": "line", "source": "vector"}, {"id": "interactive-layer", "type": "line", "source": "vector", "source-layer": "source-layer", "interactive": true}, {"id": "line-gradient-bad", "type": "line", "source": "vector", "source-layer": "source-layer", "paint": {"line-gradient": ["interpolate", ["linear"], ["line-progress"], 0, "#000000"]}}, {"id": "line-gradient-missing-lineMetrics", "type": "line", "source": "g<PERSON><PERSON><PERSON>", "source-layer": "source-layer", "paint": {"line-gradient": ["interpolate", ["linear"], ["line-progress"], 0, "#000000"]}}, {"id": "line-gradient-good", "type": "line", "source": "lineMetrics", "source-layer": "source-layer", "paint": {"line-gradient": ["interpolate", ["linear"], ["line-progress"], 0, "#000000"]}}, {"id": "custom", "type": "custom", "source": "vector", "source-layer": "layer"}]}