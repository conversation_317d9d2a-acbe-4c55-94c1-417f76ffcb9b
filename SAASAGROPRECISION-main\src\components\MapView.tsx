import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Layers, Satellite, Navigation, ZoomIn, ZoomOut, RotateCcw, Filter, <PERSON>hair, AlertCircle } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ile<PERSON><PERSON>er, <PERSON>er, Popup, useMap, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import LocationInfo from './LocationInfo';

// Fix para ícones do Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Ícones customizados para diferentes status
const createCustomIcon = (color: string) => {
  return L.divIcon({
    className: 'custom-div-icon',
    html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
    iconSize: [20, 20],
    iconAnchor: [10, 10]
  });
};

const farmIcons = {
  healthy: createCustomIcon('#10b981'),
  warning: createCustomIcon('#f59e0b'),
  critical: createCustomIcon('#ef4444'),
  user: L.divIcon({
    className: 'custom-div-icon',
    html: `<div style="background-color: #3b82f6; width: 16px; height: 16px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
    iconSize: [16, 16],
    iconAnchor: [8, 8]
  })
};

// Componente para localização do usuário
const LocationMarker: React.FC<{ position: [number, number] | null }> = ({ position }) => {
  const map = useMap();

  useMapEvents({
    locationfound(e) {
      map.flyTo(e.latlng, map.getZoom());
    },
  });

  return position === null ? null : (
    <Marker position={position} icon={farmIcons.user}>
      <Popup>Sua localização atual</Popup>
    </Marker>
  );
};

// Componente para controles do mapa
const MapControls: React.FC<{ onLocate: () => void }> = ({ onLocate }) => {
  const map = useMap();

  return (
    <div className="leaflet-top leaflet-right">
      <div className="leaflet-control leaflet-bar">
        <button
          className="leaflet-control-button"
          onClick={() => map.zoomIn()}
          title="Zoom In"
          style={{ width: '30px', height: '30px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          +
        </button>
        <button
          className="leaflet-control-button"
          onClick={() => map.zoomOut()}
          title="Zoom Out"
          style={{ width: '30px', height: '30px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          -
        </button>
        <button
          className="leaflet-control-button"
          onClick={onLocate}
          title="Minha Localização"
          style={{ width: '30px', height: '30px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          📍
        </button>
      </div>
    </div>
  );
};

const MapView: React.FC = () => {
  const [selectedLayer, setSelectedLayer] = useState('satellite');
  const [selectedFarm, setSelectedFarm] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([-12.5489, -55.7183]);
  const [mapZoom, setMapZoom] = useState(10);
  const mapRef = useRef<L.Map | null>(null);

  const farms = [
    {
      id: 1,
      name: 'Fazenda São João',
      coordinates: { lat: -12.5489, lng: -55.7183 },
      area: 2450,
      status: 'healthy',
      ndvi: 0.75,
      alerts: 1,
      description: 'Propriedade com cultivo de soja e milho',
      owner: 'João Silva'
    },
    {
      id: 2,
      name: 'Fazenda Esperança',
      coordinates: { lat: -15.5561, lng: -54.2961 },
      area: 1890,
      status: 'warning',
      ndvi: 0.62,
      alerts: 3,
      description: 'Fazenda especializada em algodão',
      owner: 'Maria Santos'
    },
    {
      id: 3,
      name: 'Fazenda Progresso',
      coordinates: { lat: -13.2500, lng: -56.1000 },
      area: 3200,
      status: 'healthy',
      ndvi: 0.82,
      alerts: 0,
      description: 'Grande propriedade com cultivos diversificados',
      owner: 'Carlos Oliveira'
    }
  ];

  // Função para obter localização GPS
  const getCurrentLocation = () => {
    setLocationError(null);

    if (!navigator.geolocation) {
      setLocationError('Geolocalização não é suportada neste navegador');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        const newLocation: [number, number] = [latitude, longitude];
        setUserLocation(newLocation);
        setMapCenter(newLocation);
        setMapZoom(15);
      },
      (error) => {
        let errorMessage = 'Erro ao obter localização';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Permissão de localização negada';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Localização indisponível';
            break;
          case error.TIMEOUT:
            errorMessage = 'Timeout ao obter localização';
            break;
        }
        setLocationError(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  };

  // Função para calcular distância entre dois pontos
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Raio da Terra em km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Obter localização automaticamente ao carregar o componente
  useEffect(() => {
    getCurrentLocation();
  }, []);

  const layers = [
    { id: 'satellite', name: 'Satélite', icon: Satellite },
    { id: 'ndvi', name: 'NDVI', icon: Layers },
    { id: 'terrain', name: 'Terreno', icon: MapPin },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'healthy': return 'Saudável';
      case 'warning': return 'Atenção';
      case 'critical': return 'Crítico';
      default: return 'Desconhecido';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mapa Interativo</h1>
          <p className="text-gray-600">Visualização geoespacial das propriedades e análises espectrais</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Map Container */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Map Controls */}
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-lg font-semibold text-gray-900">Visualização</h3>
                  <div className="flex items-center space-x-2">
                    {layers.map((layer) => {
                      const Icon = layer.icon;
                      return (
                        <button
                          key={layer.id}
                          onClick={() => setSelectedLayer(layer.id)}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            selectedLayer === layer.id
                              ? 'bg-emerald-100 text-emerald-700'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span>{layer.name}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Filtros"
                  >
                    <Filter className="w-5 h-5" />
                  </button>
                  <button
                    onClick={getCurrentLocation}
                    className={`p-2 rounded-lg transition-colors ${
                      userLocation
                        ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Minha localização"
                  >
                    <Navigation className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => {
                      setMapCenter([-12.5489, -55.7183]);
                      setMapZoom(10);
                    }}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Centralizar no Brasil"
                  >
                    <RotateCcw className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Mapa Interativo Real */}
              <div className="relative h-96">
                {locationError && (
                  <div className="absolute top-2 left-2 z-[1000] bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded-lg text-sm flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4" />
                    <span>{locationError}</span>
                  </div>
                )}

                <MapContainer
                  center={mapCenter}
                  zoom={mapZoom}
                  style={{ height: '100%', width: '100%' }}
                  ref={mapRef}
                >
                  {/* Camadas de Mapa */}
                  {selectedLayer === 'satellite' && (
                    <TileLayer
                      url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                      attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
                    />
                  )}
                  {selectedLayer === 'terrain' && (
                    <TileLayer
                      url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://opentopomap.org/">OpenTopoMap</a>'
                    />
                  )}
                  {selectedLayer === 'ndvi' && (
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://openstreetmap.org/">OpenStreetMap</a>'
                      opacity={0.7}
                    />
                  )}

                  {/* Marcadores das Fazendas */}
                  {farms.map((farm) => (
                    <Marker
                      key={farm.id}
                      position={[farm.coordinates.lat, farm.coordinates.lng]}
                      icon={farmIcons[farm.status as keyof typeof farmIcons]}
                      eventHandlers={{
                        click: () => setSelectedFarm(selectedFarm === farm.id ? null : farm.id)
                      }}
                    >
                      <Popup>
                        <div className="p-2">
                          <h3 className="font-semibold text-gray-900">{farm.name}</h3>
                          <p className="text-sm text-gray-600 mb-2">{farm.description}</p>
                          <div className="space-y-1 text-xs">
                            <div><strong>Proprietário:</strong> {farm.owner}</div>
                            <div><strong>Área:</strong> {farm.area} ha</div>
                            <div><strong>NDVI:</strong> {farm.ndvi}</div>
                            <div><strong>Status:</strong> {getStatusLabel(farm.status)}</div>
                            {farm.alerts > 0 && (
                              <div className="text-yellow-600"><strong>Alertas:</strong> {farm.alerts}</div>
                            )}
                            {userLocation && (
                              <div><strong>Distância:</strong> {
                                calculateDistance(
                                  userLocation[0], userLocation[1],
                                  farm.coordinates.lat, farm.coordinates.lng
                                ).toFixed(1)
                              } km</div>
                            )}
                          </div>
                        </div>
                      </Popup>
                    </Marker>
                  ))}

                  {/* Marcador da Localização do Usuário */}
                  <LocationMarker position={userLocation} />

                  {/* Controles Customizados */}
                  <MapControls onLocate={getCurrentLocation} />
                </MapContainer>

                {/* Botão de Localização */}
                <button
                  onClick={getCurrentLocation}
                  className="absolute bottom-4 right-4 z-[1000] bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                  title="Obter minha localização"
                >
                  <Crosshair className="w-5 h-5" />
                </button>

                {/* Legend */}
                <div className="absolute bottom-4 left-4 z-[1000] bg-white rounded-lg shadow-lg p-3">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Legenda</h4>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-green-500" />
                      <span className="text-xs text-gray-600">Saudável</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-yellow-500" />
                      <span className="text-xs text-gray-600">Atenção</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500" />
                      <span className="text-xs text-gray-600">Crítico</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                      <span className="text-xs text-gray-600">Sua localização</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Filters Panel */}
              {showFilters && (
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Filtros</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Status da Vegetação</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="healthy">Saudável</option>
                        <option value="warning">Atenção</option>
                        <option value="critical">Crítico</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Faixa NDVI</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todas</option>
                        <option value="high">Alto (0.7-1.0)</option>
                        <option value="medium">Médio (0.4-0.7)</option>
                        <option value="low">Baixo (0.0-0.4)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Tipo de Cultivo</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="soja">Soja</option>
                        <option value="milho">Milho</option>
                        <option value="algodao">Algodão</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Location Info */}
            <LocationInfo
              position={userLocation}
              onLocationUpdate={(data) => {
                setUserLocation([data.latitude, data.longitude]);
              }}
            />

            {/* Farm List */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Propriedades</h3>
              <div className="space-y-3">
                {farms.map((farm) => (
                  <div
                    key={farm.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedFarm === farm.id
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedFarm(selectedFarm === farm.id ? null : farm.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{farm.name}</h4>
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(farm.status)}`} />
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Área: {farm.area} ha</p>
                      <p>NDVI: {farm.ndvi}</p>
                      <p>Status: {getStatusLabel(farm.status)}</p>
                      {farm.alerts > 0 && (
                        <p className="text-yellow-600">{farm.alerts} alerta(s)</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Farm Details */}
            {selectedFarm && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalhes</h3>
                {(() => {
                  const farm = farms.find(f => f.id === selectedFarm);
                  if (!farm) return null;

                  return (
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Coordenadas</label>
                        <p className="text-sm text-gray-900">
                          {farm.coordinates.lat.toFixed(6)}, {farm.coordinates.lng.toFixed(6)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Área Total</label>
                        <p className="text-sm text-gray-900">{farm.area} hectares</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">NDVI Atual</label>
                        <p className="text-sm text-gray-900">{farm.ndvi}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status</label>
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(farm.status)}`} />
                          <span className="text-sm text-gray-900">{getStatusLabel(farm.status)}</span>
                        </div>
                      </div>
                      {userLocation && (
                        <div>
                          <label className="text-sm font-medium text-gray-700">Distância</label>
                          <p className="text-sm text-gray-900">
                            {calculateDistance(
                              userLocation[0], userLocation[1],
                              farm.coordinates.lat, farm.coordinates.lng
                            ).toFixed(1)} km da sua localização
                          </p>
                        </div>
                      )}
                      <div>
                        <label className="text-sm font-medium text-gray-700">Proprietário</label>
                        <p className="text-sm text-gray-900">{farm.owner}</p>
                      </div>
                      {farm.alerts > 0 && (
                        <div>
                          <label className="text-sm font-medium text-gray-700">Alertas</label>
                          <p className="text-sm text-yellow-600">{farm.alerts} alerta(s) ativo(s)</p>
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapView;
