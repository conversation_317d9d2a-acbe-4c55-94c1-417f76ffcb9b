[{"message": "layers[4].layout.text-font: Invalid data expression for \"text-font\". Output values must be contained as literals within the expression.", "line": 57}, {"message": "layers[5].layout.text-font: Invalid data expression for \"text-font\". Output values must be contained as literals within the expression.", "line": 67}, {"message": "layers[6].layout.text-font: Invalid data expression for \"text-font\". Output values must be contained as literals within the expression.", "line": 77}, {"message": "layers[7].layout.text-font: Invalid data expression for \"text-font\". Output values must be contained as literals within the expression.", "line": 87}, {"message": "layers[8].layout.text-font: Invalid data expression for \"text-font\". Output values must be contained as literals within the expression.", "line": 97}, {"message": "layers[9].layout.text-font: Invalid data expression for \"text-font\". Output values must be contained as literals within the expression.", "line": 107}, {"message": "layers[10].layout.text-font: Invalid data expression for \"text-font\". Output values must be contained as literals within the expression.", "line": 117}, {"message": "layers[11].layout.text-font: \"text-font\" does not support identity functions", "line": 127}]