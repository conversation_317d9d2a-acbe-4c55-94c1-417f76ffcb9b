@media (min-width: 1050px) {
  .toc_accordion {
    display: none;
  }
}

.toc_accordion {
  border: 1px solid rgb(229, 229, 229);
  border-radius: 10px;
  padding: 10px;
}

.scroll_to_top {
  width: 50px;
  height: 50px;
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 30;
  background-color: white;
  border: 1px solid grey;
  cursor: pointer;
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
}

.main_content_container {
  flex-grow: 1;

  width: 100%;
  display: flex;
  box-sizing: border-box;
  overflow-x: auto;
  justify-content: center;

  .header-anchor {
    color: black !important;
  }

  .main_content_padding_container {
    max-width: 800px;

    position: relative;
    padding: 1rem 1rem 5rem 1rem;
    overflow-x: auto;
    box-sizing: border-box;

    .row {
      display: flex;
      box-sizing: border-box;

      .doc_items {
        flex: 1 0;
        margin-left: 0;
        width: 100%;

        h2,
        h3 {
          padding-top: calc(3.75rem * 1 + 20px);
          margin-top: calc(3.75rem * (-1) + 26px);
        }
      }
    }
  }
}

.github_link {
  text-align: center;
  width: 100%;
  display: block;
  margin: 20px auto 0 auto;
  font-size: 16px;
  color: black;
}
