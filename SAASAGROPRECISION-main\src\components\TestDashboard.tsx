import React from 'react';
import { Bar<PERSON>hart3, TrendingUp, Al<PERSON><PERSON>riangle, Leaf } from 'lucide-react';

const TestDashboard: React.FC = () => {
  const stats = [
    {
      title: 'Área Total Monitorada',
      value: '2.450 ha',
      change: '+5%',
      changeType: 'positive',
      icon: Leaf,
      color: 'emerald'
    },
    {
      title: 'NDVI Médio',
      value: '0.75',
      change: '+0.08',
      changeType: 'positive',
      icon: BarChart3,
      color: 'blue'
    },
    {
      title: 'Alertas Ativos',
      value: '3',
      change: '-2',
      changeType: 'negative',
      icon: AlertTriangle,
      color: 'red'
    }
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Agrícola</h1>
          <p className="text-gray-600">Visão geral do monitoramento das suas propriedades rurais</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                    <div className="flex items-center mt-2">
                      <span className={`text-sm font-medium ${
                        stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                      <span className="text-sm text-gray-500 ml-1">vs mês anterior</span>
                    </div>
                  </div>
                  <div className={`p-3 rounded-lg ${
                    stat.color === 'emerald' ? 'bg-emerald-100' :
                    stat.color === 'blue' ? 'bg-blue-100' : 'bg-red-100'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      stat.color === 'emerald' ? 'text-emerald-600' :
                      stat.color === 'blue' ? 'text-blue-600' : 'text-red-600'
                    }`} />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Simple Content */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Dashboard Integrado com Sucesso!</h3>
          <p className="text-gray-600 mb-4">
            O dashboard foi integrado com sucesso à landing page. Agora você pode navegar entre:
          </p>
          <ul className="list-disc list-inside space-y-2 text-gray-600">
            <li>Dashboard principal com métricas</li>
            <li>Mapa interativo com localização GPS</li>
            <li>Gestão de fazendas e talhões</li>
            <li>Análise de imagens de drone</li>
            <li>Analytics avançados</li>
            <li>Relatórios detalhados</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TestDashboard;
