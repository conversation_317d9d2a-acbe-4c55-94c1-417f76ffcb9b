{"version": 3, "sources": ["../src/index.ts", "../src/components/map.tsx", "../src/components/use-map.tsx", "../src/utils/deep-equal.ts", "../src/utils/transform.ts", "../src/utils/style-utils.ts", "../src/maplibre/maplibre.ts", "../src/maplibre/create-ref.ts", "../src/utils/use-isomorphic-layout-effect.ts", "../src/utils/set-globals.ts", "../src/components/marker.ts", "../src/utils/apply-react-style.ts", "../src/utils/compare-class-names.ts", "../src/components/popup.ts", "../src/components/attribution-control.ts", "../src/components/use-control.ts", "../src/components/fullscreen-control.ts", "../src/components/geolocate-control.ts", "../src/components/navigation-control.ts", "../src/components/scale-control.ts", "../src/components/terrain-control.ts", "../src/components/logo-control.ts", "../src/components/source.ts", "../src/utils/assert.ts", "../src/components/layer.ts"], "sourcesContent": ["import {Map} from './components/map';\nexport {Map};\nexport default Map;\n\nexport {Marker} from './components/marker';\nexport {Popup} from './components/popup';\nexport {AttributionControl} from './components/attribution-control';\nexport {FullscreenControl} from './components/fullscreen-control';\nexport {GeolocateControl} from './components/geolocate-control';\nexport {NavigationControl} from './components/navigation-control';\nexport {ScaleControl} from './components/scale-control';\nexport {TerrainControl} from './components/terrain-control';\nexport {LogoControl} from './components/logo-control';\nexport {Source} from './components/source';\nexport {Layer} from './components/layer';\nexport {useControl} from './components/use-control';\nexport {MapProvider, useMap} from './components/use-map';\n\nexport type {MapProps} from './components/map';\nexport type {MapRef} from './maplibre/create-ref';\nexport type {MarkerProps} from './components/marker';\nexport type {PopupProps} from './components/popup';\nexport type {AttributionControlProps} from './components/attribution-control';\nexport type {FullscreenControlProps} from './components/fullscreen-control';\nexport type {GeolocateControlProps} from './components/geolocate-control';\nexport type {NavigationControlProps} from './components/navigation-control';\nexport type {ScaleControlProps} from './components/scale-control';\nexport type {TerrainControlProps} from './components/terrain-control';\nexport type {LogoControlProps} from './components/logo-control';\nexport type {SourceProps} from './components/source';\nexport type {LayerProps} from './components/layer';\n\n// Types\nexport * from './types/common';\nexport * from './types/events';\nexport * from './types/lib';\nexport * from './types/style-spec';\n", "import * as React from 'react';\nimport {useState, useRef, useEffect, useContext, useMemo, useImperativeHandle} from 'react';\n\nimport {MountedMapsContext} from './use-map';\nimport Maplibre, {MaplibreProps} from '../maplibre/maplibre';\nimport createRef, {MapRef} from '../maplibre/create-ref';\n\nimport type {CSSProperties} from 'react';\nimport useIsomorphicLayoutEffect from '../utils/use-isomorphic-layout-effect';\nimport setGlobals, {GlobalSettings} from '../utils/set-globals';\nimport type {MapLib, MapOptions} from '../types/lib';\n\nexport type MapContextValue = {\n  mapLib: MapLib;\n  map: MapRef;\n};\n\nexport const MapContext = React.createContext<MapContextValue>(null);\n\ntype MapInitOptions = Omit<\n  MapOptions,\n  'style' | 'container' | 'bounds' | 'fitBoundsOptions' | 'center'\n>;\n\nexport type MapProps = MapInitOptions &\n  MaplibreProps &\n  GlobalSettings & {\n    mapLib?: MapLib | Promise<MapLib>;\n    reuseMaps?: boolean;\n    /** Map container id */\n    id?: string;\n    /** Map container CSS style */\n    style?: CSSProperties;\n    children?: any;\n  };\n\nfunction _Map(props: MapProps, ref: React.Ref<MapRef>) {\n  const mountedMapsContext = useContext(MountedMapsContext);\n  const [mapInstance, setMapInstance] = useState<Maplibre>(null);\n  const containerRef = useRef();\n\n  const {current: contextValue} = useRef<MapContextValue>({mapLib: null, map: null});\n\n  useEffect(() => {\n    const mapLib = props.mapLib;\n    let isMounted = true;\n    let maplibre: Maplibre;\n\n    Promise.resolve(mapLib || import('maplibre-gl'))\n      .then((module: MapLib | {default: MapLib}) => {\n        if (!isMounted) {\n          return;\n        }\n        if (!module) {\n          throw new Error('Invalid mapLib');\n        }\n        const mapboxgl = 'Map' in module ? module : module.default;\n        if (!mapboxgl.Map) {\n          throw new Error('Invalid mapLib');\n        }\n\n        setGlobals(mapboxgl, props);\n        if (props.reuseMaps) {\n          maplibre = Maplibre.reuse(props, containerRef.current);\n        }\n        if (!maplibre) {\n          maplibre = new Maplibre(mapboxgl.Map, props, containerRef.current);\n        }\n        contextValue.map = createRef(maplibre);\n        contextValue.mapLib = mapboxgl;\n\n        setMapInstance(maplibre);\n        mountedMapsContext?.onMapMount(contextValue.map, props.id);\n      })\n      .catch(error => {\n        const {onError} = props;\n        if (onError) {\n          onError({\n            type: 'error',\n            target: null,\n            originalEvent: null,\n            error\n          });\n        } else {\n          console.error(error); // eslint-disable-line\n        }\n      });\n\n    return () => {\n      isMounted = false;\n      if (maplibre) {\n        mountedMapsContext?.onMapUnmount(props.id);\n        if (props.reuseMaps) {\n          maplibre.recycle();\n        } else {\n          maplibre.destroy();\n        }\n      }\n    };\n  }, []);\n\n  useIsomorphicLayoutEffect(() => {\n    if (mapInstance) {\n      mapInstance.setProps(props);\n    }\n  });\n\n  useImperativeHandle(ref, () => contextValue.map, [mapInstance]);\n\n  const style: CSSProperties = useMemo(\n    () => ({\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      ...props.style\n    }),\n    [props.style]\n  );\n\n  const CHILD_CONTAINER_STYLE = {\n    height: '100%'\n  };\n\n  return (\n    <div id={props.id} ref={containerRef} style={style}>\n      {mapInstance && (\n        <MapContext.Provider value={contextValue}>\n          <div mapboxgl-children=\"\" style={CHILD_CONTAINER_STYLE}>\n            {props.children}\n          </div>\n        </MapContext.Provider>\n      )}\n    </div>\n  );\n}\n\nexport const Map = React.forwardRef(_Map);\n", "import * as React from 'react';\nimport {useState, useCallback, useMemo, useContext} from 'react';\n\nimport {MapRef} from '../maplibre/create-ref';\nimport {MapContext} from './map';\n\ntype MountedMapsContextValue = {\n  maps: {[id: string]: MapRef};\n  onMapMount: (map: MapRef, id: string) => void;\n  onMapUnmount: (id: string) => void;\n};\n\nexport const MountedMapsContext = React.createContext<MountedMapsContextValue>(null);\n\nexport const MapProvider: React.FC<{children?: React.ReactNode}> = props => {\n  const [maps, setMaps] = useState<{[id: string]: MapRef}>({});\n\n  const onMapMount = useCallback((map: MapRef, id: string = 'default') => {\n    setMaps(currMaps => {\n      if (id === 'current') {\n        throw new Error(\"'current' cannot be used as map id\");\n      }\n      if (currMaps[id]) {\n        throw new Error(`Multiple maps with the same id: ${id}`);\n      }\n      return {...currMaps, [id]: map};\n    });\n  }, []);\n\n  const onMapUnmount = useCallback((id: string = 'default') => {\n    setMaps(currMaps => {\n      if (currMaps[id]) {\n        const nextMaps = {...currMaps};\n        delete nextMaps[id];\n        return nextMaps;\n      }\n      return currMaps;\n    });\n  }, []);\n\n  return (\n    <MountedMapsContext.Provider\n      value={{\n        maps,\n        onMapMount,\n        onMapUnmount\n      }}\n    >\n      {props.children}\n    </MountedMapsContext.Provider>\n  );\n};\n\nexport type MapCollection = {\n  [id: string]: MapRef | undefined;\n  current?: MapRef;\n};\n\nexport function useMap(): MapCollection {\n  const maps = useContext(MountedMapsContext)?.maps;\n  const currentMap = useContext(MapContext);\n\n  const mapsWithCurrent = useMemo(() => {\n    return {...maps, current: currentMap?.map};\n  }, [maps, currentMap]);\n\n  return mapsWithCurrent as MapCollection;\n}\n", "import type {PointLike} from '../types/common';\n\n/**\n * Compare two points\n * @param a\n * @param b\n * @returns true if the points are equal\n */\nexport function arePointsEqual(a?: PointLike, b?: PointLike): boolean {\n  const ax = Array.isArray(a) ? a[0] : a ? a.x : 0;\n  const ay = Array.isArray(a) ? a[1] : a ? a.y : 0;\n  const bx = Array.isArray(b) ? b[0] : b ? b.x : 0;\n  const by = Array.isArray(b) ? b[1] : b ? b.y : 0;\n  return ax === bx && ay === by;\n}\n\n/* eslint-disable complexity */\n/**\n * Compare any two objects\n * @param a\n * @param b\n * @returns true if the objects are deep equal\n */\nexport function deepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true;\n  }\n  if (!a || !b) {\n    return false;\n  }\n  if (Array.isArray(a)) {\n    if (!Array.isArray(b) || a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (!deepEqual(a[i], b[i])) {\n        return false;\n      }\n    }\n    return true;\n  } else if (Array.isArray(b)) {\n    return false;\n  }\n  if (typeof a === 'object' && typeof b === 'object') {\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    if (aKeys.length !== bKeys.length) {\n      return false;\n    }\n    for (const key of aKeys) {\n      if (!b.hasOwnProperty(key)) {\n        return false;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return false;\n}\n", "import type {MaplibreProps} from '../maplibre/maplibre';\nimport type {ViewState} from '../types/common';\nimport type {TransformLike} from '../types/internal';\nimport {deepEqual} from './deep-equal';\n\n/**\n * Capture a transform's current state\n * @param transform\n * @returns descriptor of the view state\n */\nexport function transformToViewState(tr: TransformLike): ViewState {\n  return {\n    longitude: tr.center.lng,\n    latitude: tr.center.lat,\n    zoom: tr.zoom,\n    pitch: tr.pitch,\n    bearing: tr.bearing,\n    padding: tr.padding\n  };\n}\n\n/* eslint-disable complexity */\n/**\n * Applies requested view state to a transform\n * @returns an object containing detected changes\n */\nexport function applyViewStateToTransform(\n  /** An object that describes Maplibre's camera state */\n  tr: TransformLike,\n  /** Props from Map component */\n  props: MaplibreProps\n): Partial<TransformLike> {\n  const v: Partial<ViewState> = props.viewState || props;\n  const changes: Partial<TransformLike> = {};\n\n  if (\n    'longitude' in v &&\n    'latitude' in v &&\n    (v.longitude !== tr.center.lng || v.latitude !== tr.center.lat)\n  ) {\n    const LngLat = tr.center.constructor;\n    // @ts-expect-error we should not import LngLat class from maplibre-gl because we don't know the source of mapLib\n    changes.center = new LngLat(v.longitude, v.latitude);\n  }\n  if ('zoom' in v && v.zoom !== tr.zoom) {\n    changes.zoom = v.zoom;\n  }\n  if ('bearing' in v && v.bearing !== tr.bearing) {\n    changes.bearing = v.bearing;\n  }\n  if ('pitch' in v && v.pitch !== tr.pitch) {\n    changes.pitch = v.pitch;\n  }\n  if (v.padding && tr.padding && !deepEqual(v.padding, tr.padding)) {\n    changes.padding = v.padding;\n  }\n  return changes;\n}\n", "import type {StyleSpecification} from '../types/style-spec';\nimport type {ImmutableLike} from '../types/common';\n\nconst refProps = ['type', 'source', 'source-layer', 'minzoom', 'maxzoom', 'filter', 'layout'];\n\n// Prepare a map style object for diffing\n// If immutable - convert to plain object\n// Work around some issues in older styles that would fail Mapbox's diffing\nexport function normalizeStyle(\n  style: string | StyleSpecification | ImmutableLike<StyleSpecification>\n): string | StyleSpecification {\n  if (!style) {\n    return null;\n  }\n  if (typeof style === 'string') {\n    return style;\n  }\n  if ('toJS' in style) {\n    style = style.toJS();\n  }\n  if (!style.layers) {\n    return style;\n  }\n  const layerIndex = {};\n\n  for (const layer of style.layers) {\n    layerIndex[layer.id] = layer;\n  }\n\n  const layers = style.layers.map(layer => {\n    let normalizedLayer: typeof layer = null;\n\n    if ('interactive' in layer) {\n      normalizedLayer = Object.assign({}, layer);\n      // Breaks style diffing :(\n      // @ts-ignore legacy field not typed\n      delete normalizedLayer.interactive;\n    }\n\n    // Style diffing doesn't work with refs so expand them out manually before diffing.\n    // @ts-ignore legacy field not typed\n    const layerRef = layerIndex[layer.ref];\n    if (layerRef) {\n      normalizedLayer = normalizedLayer || Object.assign({}, layer);\n      // @ts-ignore\n      delete normalizedLayer.ref;\n      // https://github.com/mapbox/mapbox-gl-js/blob/master/src/style-spec/deref.js\n      for (const propName of refProps) {\n        if (propName in layerRef) {\n          normalizedLayer[propName] = layerRef[propName];\n        }\n      }\n    }\n\n    return normalizedLayer || layer;\n  });\n\n  // Do not mutate the style object provided by the user\n  return {...style, layers};\n}\n", "import {transformToViewState, applyViewStateToTransform} from '../utils/transform';\nimport {normalizeStyle} from '../utils/style-utils';\nimport {deepEqual} from '../utils/deep-equal';\n\nimport type {TransformLike} from '../types/internal';\nimport type {\n  ViewState,\n  Point,\n  PointLike,\n  PaddingOptions,\n  ImmutableLike,\n  LngLatBoundsLike,\n  MapGeoJSONFeature\n} from '../types/common';\nimport type {\n  StyleSpecification,\n  SkySpecification,\n  LightSpecification,\n  TerrainSpecification,\n  ProjectionSpecification\n} from '../types/style-spec';\nimport type {MapInstance} from '../types/lib';\nimport type {\n  MapCallbacks,\n  ViewStateChangeEvent,\n  MapEvent,\n  ErrorEvent,\n  MapMouseEvent\n} from '../types/events';\n\nexport type MaplibreProps = Partial<ViewState> &\n  MapCallbacks & {\n    /** Camera options used when constructing the Map instance */\n    initialViewState?: Partial<ViewState> & {\n      /** The initial bounds of the map. If bounds is specified, it overrides longitude, latitude and zoom options. */\n      bounds?: LngLatBoundsLike;\n      /** A fitBounds options object to use only when setting the bounds option. */\n      fitBoundsOptions?: {\n        offset?: PointLike;\n        minZoom?: number;\n        maxZoom?: number;\n        padding?: number | PaddingOptions;\n      };\n    };\n\n    /** If provided, render into an external WebGL context */\n    gl?: WebGLRenderingContext;\n\n    /** For external controller to override the camera state */\n    viewState?: ViewState & {\n      width: number;\n      height: number;\n    };\n\n    // Styling\n\n    /** Mapbox style */\n    mapStyle?: string | StyleSpecification | ImmutableLike<StyleSpecification>;\n    /** Enable diffing when the map style changes\n     * @default true\n     */\n    styleDiffing?: boolean;\n    /** The projection property of the style. Must conform to the Projection Style Specification.\n     * @default 'mercator'\n     */\n    projection?: ProjectionSpecification | 'mercator' | 'globe';\n    /** Light properties of the map. */\n    light?: LightSpecification;\n    /** Terrain property of the style. Must conform to the Terrain Style Specification.\n     * If `undefined` is provided, removes terrain from the map. */\n    terrain?: TerrainSpecification;\n    /** Sky properties of the map. Must conform to the Sky Style Specification. */\n    sky?: SkySpecification;\n\n    /** Default layers to query on pointer events */\n    interactiveLayerIds?: string[];\n    /** CSS cursor */\n    cursor?: string;\n  };\n\nconst DEFAULT_STYLE = {version: 8, sources: {}, layers: []} as StyleSpecification;\n\nconst pointerEvents = {\n  mousedown: 'onMouseDown',\n  mouseup: 'onMouseUp',\n  mouseover: 'onMouseOver',\n  mousemove: 'onMouseMove',\n  click: 'onClick',\n  dblclick: 'onDblClick',\n  mouseenter: 'onMouseEnter',\n  mouseleave: 'onMouseLeave',\n  mouseout: 'onMouseOut',\n  contextmenu: 'onContextMenu',\n  touchstart: 'onTouchStart',\n  touchend: 'onTouchEnd',\n  touchmove: 'onTouchMove',\n  touchcancel: 'onTouchCancel'\n};\nconst cameraEvents = {\n  movestart: 'onMoveStart',\n  move: 'onMove',\n  moveend: 'onMoveEnd',\n  dragstart: 'onDragStart',\n  drag: 'onDrag',\n  dragend: 'onDragEnd',\n  zoomstart: 'onZoomStart',\n  zoom: 'onZoom',\n  zoomend: 'onZoomEnd',\n  rotatestart: 'onRotateStart',\n  rotate: 'onRotate',\n  rotateend: 'onRotateEnd',\n  pitchstart: 'onPitchStart',\n  pitch: 'onPitch',\n  pitchend: 'onPitchEnd'\n};\nconst otherEvents = {\n  wheel: 'onWheel',\n  boxzoomstart: 'onBoxZoomStart',\n  boxzoomend: 'onBoxZoomEnd',\n  boxzoomcancel: 'onBoxZoomCancel',\n  resize: 'onResize',\n  load: 'onLoad',\n  render: 'onRender',\n  idle: 'onIdle',\n  remove: 'onRemove',\n  data: 'onData',\n  styledata: 'onStyleData',\n  sourcedata: 'onSourceData',\n  error: 'onError'\n};\nconst settingNames = [\n  'minZoom',\n  'maxZoom',\n  'minPitch',\n  'maxPitch',\n  'maxBounds',\n  'projection',\n  'renderWorldCopies'\n];\nconst handlerNames = [\n  'scrollZoom',\n  'boxZoom',\n  'dragRotate',\n  'dragPan',\n  'keyboard',\n  'doubleClickZoom',\n  'touchZoomRotate',\n  'touchPitch'\n];\n\n/**\n * A wrapper for mapbox-gl's Map class\n */\nexport default class Maplibre {\n  private _MapClass: {new (options: any): MapInstance};\n  // mapboxgl.Map instance\n  private _map: MapInstance = null;\n  // User-supplied props\n  props: MaplibreProps;\n\n  // Internal states\n  private _internalUpdate: boolean = false;\n  private _hoveredFeatures: MapGeoJSONFeature[] = null;\n  private _propsedCameraUpdate: ViewState | null = null;\n  private _styleComponents: {\n    light?: LightSpecification;\n    sky?: SkySpecification;\n    projection?: ProjectionSpecification;\n    terrain?: TerrainSpecification | null;\n  } = {};\n\n  static savedMaps: Maplibre[] = [];\n\n  constructor(\n    MapClass: {new (options: any): MapInstance},\n    props: MaplibreProps,\n    container: HTMLDivElement\n  ) {\n    this._MapClass = MapClass;\n    this.props = props;\n    this._initialize(container);\n  }\n\n  get map(): MapInstance {\n    return this._map;\n  }\n\n  setProps(props: MaplibreProps) {\n    const oldProps = this.props;\n    this.props = props;\n\n    const settingsChanged = this._updateSettings(props, oldProps);\n    const sizeChanged = this._updateSize(props);\n    const viewStateChanged = this._updateViewState(props);\n    this._updateStyle(props, oldProps);\n    this._updateStyleComponents(props);\n    this._updateHandlers(props, oldProps);\n\n    // If 1) view state has changed to match props and\n    //    2) the props change is not triggered by map events,\n    // it's driven by an external state change. Redraw immediately\n    if (settingsChanged || sizeChanged || (viewStateChanged && !this._map.isMoving())) {\n      this.redraw();\n    }\n  }\n\n  static reuse(props: MaplibreProps, container: HTMLDivElement): Maplibre {\n    const that = Maplibre.savedMaps.pop();\n    if (!that) {\n      return null;\n    }\n\n    const map = that.map;\n    // When reusing the saved map, we need to reparent the map(canvas) and other child nodes\n    // intoto the new container from the props.\n    // Step 1: reparenting child nodes from old container to new container\n    const oldContainer = map.getContainer();\n    container.className = oldContainer.className;\n    while (oldContainer.childNodes.length > 0) {\n      container.appendChild(oldContainer.childNodes[0]);\n    }\n    // Step 2: replace the internal container with new container from the react component\n    // @ts-ignore\n    map._container = container;\n\n    // With maplibre-gl as mapLib, map uses ResizeObserver to observe when its container resizes.\n    // When reusing the saved map, we need to disconnect the observer and observe the new container.\n    // Step 3: telling the ResizeObserver to disconnect and observe the new container\n    // @ts-ignore\n    const resizeObserver = map._resizeObserver;\n    if (resizeObserver) {\n      resizeObserver.disconnect();\n      resizeObserver.observe(container);\n    }\n\n    // Step 4: apply new props\n    that.setProps({...props, styleDiffing: false});\n    map.resize();\n    const {initialViewState} = props;\n    if (initialViewState) {\n      if (initialViewState.bounds) {\n        map.fitBounds(initialViewState.bounds, {...initialViewState.fitBoundsOptions, duration: 0});\n      } else {\n        that._updateViewState(initialViewState);\n      }\n    }\n\n    // Simulate load event\n    if (map.isStyleLoaded()) {\n      map.fire('load');\n    } else {\n      map.once('style.load', () => map.fire('load'));\n    }\n\n    // Force reload\n    // @ts-ignore\n    map._update();\n    return that;\n  }\n\n  /* eslint-disable complexity,max-statements */\n  private _initialize(container: HTMLDivElement) {\n    const {props} = this;\n    const {mapStyle = DEFAULT_STYLE} = props;\n    const mapOptions = {\n      ...props,\n      ...props.initialViewState,\n      container,\n      style: normalizeStyle(mapStyle)\n    };\n\n    const viewState = mapOptions.initialViewState || mapOptions.viewState || mapOptions;\n    Object.assign(mapOptions, {\n      center: [viewState.longitude || 0, viewState.latitude || 0],\n      zoom: viewState.zoom || 0,\n      pitch: viewState.pitch || 0,\n      bearing: viewState.bearing || 0\n    });\n\n    if (props.gl) {\n      // eslint-disable-next-line\n      const getContext = HTMLCanvasElement.prototype.getContext;\n      // Hijack canvas.getContext to return our own WebGLContext\n      // This will be called inside the mapboxgl.Map constructor\n      // @ts-expect-error\n      HTMLCanvasElement.prototype.getContext = () => {\n        // Unhijack immediately\n        HTMLCanvasElement.prototype.getContext = getContext;\n        return props.gl;\n      };\n    }\n\n    const map = new this._MapClass(mapOptions);\n    // Props that are not part of constructor options\n    if (viewState.padding) {\n      map.setPadding(viewState.padding);\n    }\n    if (props.cursor) {\n      map.getCanvas().style.cursor = props.cursor;\n    }\n\n    // add listeners\n    map.transformCameraUpdate = this._onCameraUpdate;\n    map.on('style.load', () => {\n      // Map style has changed, this would have wiped out all settings from props\n      this._styleComponents = {\n        light: map.getLight(),\n        sky: map.getSky(),\n        // @ts-ignore getProjection() does not exist in v4\n        projection: map.getProjection?.(),\n        terrain: map.getTerrain()\n      };\n      this._updateStyleComponents(this.props);\n    });\n    map.on('sourcedata', () => {\n      // Some sources have loaded, we may need them to attach terrain\n      this._updateStyleComponents(this.props);\n    });\n    for (const eventName in pointerEvents) {\n      map.on(eventName, this._onPointerEvent);\n    }\n    for (const eventName in cameraEvents) {\n      map.on(eventName, this._onCameraEvent);\n    }\n    for (const eventName in otherEvents) {\n      map.on(eventName, this._onEvent);\n    }\n    this._map = map;\n  }\n  /* eslint-enable complexity,max-statements */\n\n  recycle() {\n    // Clean up unnecessary elements before storing for reuse.\n    const container = this.map.getContainer();\n    const children = container.querySelector('[mapboxgl-children]');\n    children?.remove();\n\n    Maplibre.savedMaps.push(this);\n  }\n\n  destroy() {\n    this._map.remove();\n  }\n\n  // Force redraw the map now. Typically resize() and jumpTo() is reflected in the next\n  // render cycle, which is managed by Mapbox's animation loop.\n  // This removes the synchronization issue caused by requestAnimationFrame.\n  redraw() {\n    const map = this._map as any;\n    // map._render will throw error if style does not exist\n    // https://github.com/mapbox/mapbox-gl-js/blob/fb9fc316da14e99ff4368f3e4faa3888fb43c513\n    //   /src/ui/map.js#L1834\n    if (map.style) {\n      // cancel the scheduled update\n      if (map._frame) {\n        map._frame.cancel();\n        map._frame = null;\n      }\n      // the order is important - render() may schedule another update\n      map._render();\n    }\n  }\n\n  /* Trigger map resize if size is controlled\n     @param {object} nextProps\n     @returns {bool} true if size has changed\n   */\n  private _updateSize(nextProps: MaplibreProps): boolean {\n    // Check if size is controlled\n    const {viewState} = nextProps;\n    if (viewState) {\n      const map = this._map;\n      if (viewState.width !== map.transform.width || viewState.height !== map.transform.height) {\n        map.resize();\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // Adapted from map.jumpTo\n  /* Update camera to match props\n     @param {object} nextProps\n     @param {bool} triggerEvents - should fire camera events\n     @returns {bool} true if anything is changed\n   */\n  private _updateViewState(nextProps: MaplibreProps): boolean {\n    const map = this._map;\n    const tr = map.transform;\n    const isMoving = map.isMoving();\n\n    // Avoid manipulating the real transform when interaction/animation is ongoing\n    // as it would interfere with Mapbox's handlers\n    if (!isMoving) {\n      const changes = applyViewStateToTransform(tr, nextProps);\n      if (Object.keys(changes).length > 0) {\n        this._internalUpdate = true;\n        map.jumpTo(changes);\n        this._internalUpdate = false;\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  /* Update camera constraints and projection settings to match props\n     @param {object} nextProps\n     @param {object} currProps\n     @returns {bool} true if anything is changed\n   */\n  private _updateSettings(nextProps: MaplibreProps, currProps: MaplibreProps): boolean {\n    const map = this._map;\n    let changed = false;\n    for (const propName of settingNames) {\n      if (propName in nextProps && !deepEqual(nextProps[propName], currProps[propName])) {\n        changed = true;\n        const setter = map[`set${propName[0].toUpperCase()}${propName.slice(1)}`];\n        setter?.call(map, nextProps[propName]);\n      }\n    }\n    return changed;\n  }\n\n  /* Update map style to match props */\n  private _updateStyle(nextProps: MaplibreProps, currProps: MaplibreProps): void {\n    if (nextProps.cursor !== currProps.cursor) {\n      this._map.getCanvas().style.cursor = nextProps.cursor || '';\n    }\n    if (nextProps.mapStyle !== currProps.mapStyle) {\n      const {mapStyle = DEFAULT_STYLE, styleDiffing = true} = nextProps;\n      const options: any = {\n        diff: styleDiffing\n      };\n      if ('localIdeographFontFamily' in nextProps) {\n        // @ts-ignore Mapbox specific prop\n        options.localIdeographFontFamily = nextProps.localIdeographFontFamily;\n      }\n      this._map.setStyle(normalizeStyle(mapStyle), options);\n    }\n  }\n\n  /* Update fog, light, projection and terrain to match props\n   * These props are special because\n   * 1. They can not be applied right away. Certain conditions (style loaded, source loaded, etc.) must be met\n   * 2. They can be overwritten by mapStyle\n   */\n  private _updateStyleComponents({light, projection, sky, terrain}: MaplibreProps): void {\n    const map = this._map;\n    const currProps = this._styleComponents;\n    // We can safely manipulate map style once it's loaded\n    if (map.style._loaded) {\n      if (light && !deepEqual(light, currProps.light)) {\n        currProps.light = light;\n        map.setLight(light);\n      }\n      if (\n        projection &&\n        !deepEqual(projection, currProps.projection) &&\n        projection !== currProps.projection?.type\n      ) {\n        currProps.projection = typeof projection === 'string' ? {type: projection} : projection;\n        // @ts-ignore setProjection does not exist in v4\n        map.setProjection?.(currProps.projection);\n      }\n      if (sky && !deepEqual(sky, currProps.sky)) {\n        currProps.sky = sky;\n        map.setSky(sky);\n      }\n      if (terrain !== undefined && !deepEqual(terrain, currProps.terrain)) {\n        if (!terrain || map.getSource(terrain.source)) {\n          currProps.terrain = terrain;\n          map.setTerrain(terrain);\n        }\n      }\n    }\n  }\n\n  /* Update interaction handlers to match props */\n  private _updateHandlers(nextProps: MaplibreProps, currProps: MaplibreProps): void {\n    const map = this._map;\n    for (const propName of handlerNames) {\n      const newValue = nextProps[propName] ?? true;\n      const oldValue = currProps[propName] ?? true;\n      if (!deepEqual(newValue, oldValue)) {\n        if (newValue) {\n          map[propName].enable(newValue);\n        } else {\n          map[propName].disable();\n        }\n      }\n    }\n  }\n\n  private _onEvent = (e: MapEvent) => {\n    // @ts-ignore\n    const cb = this.props[otherEvents[e.type]];\n    if (cb) {\n      cb(e);\n    } else if (e.type === 'error') {\n      console.error((e as ErrorEvent).error); // eslint-disable-line\n    }\n  };\n\n  private _onCameraEvent = (e: ViewStateChangeEvent) => {\n    if (this._internalUpdate) {\n      return;\n    }\n    e.viewState = this._propsedCameraUpdate || transformToViewState(this._map.transform);\n    // @ts-ignore\n    const cb = this.props[cameraEvents[e.type]];\n    if (cb) {\n      cb(e);\n    }\n  };\n\n  private _onCameraUpdate = (tr: TransformLike) => {\n    if (this._internalUpdate) {\n      return tr;\n    }\n    this._propsedCameraUpdate = transformToViewState(tr);\n    return applyViewStateToTransform(tr, this.props);\n  };\n\n  private _queryRenderedFeatures(point: Point) {\n    const map = this._map;\n    const {interactiveLayerIds = []} = this.props;\n    try {\n      return map.queryRenderedFeatures(point, {\n        layers: interactiveLayerIds.filter(map.getLayer.bind(map))\n      });\n    } catch {\n      // May fail if style is not loaded\n      return [];\n    }\n  }\n\n  private _updateHover(e: MapMouseEvent) {\n    const {props} = this;\n    const shouldTrackHoveredFeatures =\n      props.interactiveLayerIds && (props.onMouseMove || props.onMouseEnter || props.onMouseLeave);\n\n    if (shouldTrackHoveredFeatures) {\n      const eventType = e.type;\n      const wasHovering = this._hoveredFeatures?.length > 0;\n      const features = this._queryRenderedFeatures(e.point);\n      const isHovering = features.length > 0;\n\n      if (!isHovering && wasHovering) {\n        e.type = 'mouseleave';\n        this._onPointerEvent(e);\n      }\n      this._hoveredFeatures = features;\n      if (isHovering && !wasHovering) {\n        e.type = 'mouseenter';\n        this._onPointerEvent(e);\n      }\n      e.type = eventType;\n    } else {\n      this._hoveredFeatures = null;\n    }\n  }\n\n  private _onPointerEvent = (e: MapMouseEvent) => {\n    if (e.type === 'mousemove' || e.type === 'mouseout') {\n      this._updateHover(e);\n    }\n\n    // @ts-ignore\n    const cb = this.props[pointerEvents[e.type]];\n    if (cb) {\n      if (this.props.interactiveLayerIds && e.type !== 'mouseover' && e.type !== 'mouseout') {\n        e.features = this._hoveredFeatures || this._queryRenderedFeatures(e.point);\n      }\n      cb(e);\n      delete e.features;\n    }\n  };\n}\n", "import type {MapInstance} from '../types/lib';\nimport type Maplibre from './maplibre';\n\n/** These methods may break the react binding if called directly */\nconst skipMethods = [\n  'setMaxBounds',\n  'setMinZoom',\n  'setMaxZoom',\n  'setMinPitch',\n  'setMaxPitch',\n  'setRenderWorldCopies',\n  'setProjection',\n  'setStyle',\n  'addSource',\n  'removeSource',\n  'addLayer',\n  'removeLayer',\n  'setLayerZoomRange',\n  'setFilter',\n  'setPaintProperty',\n  'setLayoutProperty',\n  'setLight',\n  'setTerrain',\n  'setFog',\n  'remove'\n] as const;\n\nexport type MapRef = {\n  getMap(): MapInstance;\n} & Omit<MapInstance, (typeof skipMethods)[number]>;\n\nexport default function createRef(mapInstance: Maplibre): MapRef | null {\n  if (!mapInstance) {\n    return null;\n  }\n\n  const map = mapInstance.map;\n  const result: any = {\n    getMap: () => map\n  };\n\n  for (const key of getMethodNames(map)) {\n    // @ts-expect-error\n    if (!(key in result) && !skipMethods.includes(key)) {\n      result[key] = map[key].bind(map);\n    }\n  }\n\n  return result;\n}\n\nfunction getMethodNames(obj: Object) {\n  const result = new Set<string>();\n\n  let proto = obj;\n  while (proto) {\n    for (const key of Object.getOwnPropertyNames(proto)) {\n      if (\n        key[0] !== '_' &&\n        typeof obj[key] === 'function' &&\n        key !== 'fire' &&\n        key !== 'setEventedParent'\n      ) {\n        result.add(key);\n      }\n    }\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Array.from(result);\n}\n", "// From https://github.com/streamich/react-use/blob/master/src/useIsomorphicLayoutEffect.ts\n// useLayoutEffect but does not trigger warning in server-side rendering\nimport {useEffect, useLayoutEffect} from 'react';\n\nconst useIsomorphicLayoutEffect = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\nexport default useIsomorphicLayoutEffect;\n", "export type GlobalSettings = {\n  /** The maximum number of images (raster tiles, sprites, icons) to load in parallel.\n   * @default 16\n   */\n  maxParallelImageRequests?: number;\n  /** The map's RTL text plugin. Necessary for supporting the Arabic and Hebrew languages, which are written right-to-left.  */\n  RTLTextPlugin?: string | {pluginUrl: string; lazy?: boolean};\n  /** The number of web workers instantiated on a page with maplibre-gl maps.\n   * @default 2\n   */\n  workerCount?: number;\n  /** Provides an interface for loading maplibre-gl's WebWorker bundle from a self-hosted URL.\n   * This is useful if your site needs to operate in a strict CSP (Content Security Policy) environment\n   * wherein you are not allowed to load JavaScript code from a Blob URL, which is default behavior. */\n  workerUrl?: string;\n};\n\nexport default function setGlobals(mapLib: any, props: GlobalSettings) {\n  const {RTLTextPlugin, maxParallelImageRequests, workerCount, workerUrl} = props;\n  if (\n    RTLTextPlugin &&\n    mapLib.getRTLTextPluginStatus &&\n    mapLib.getRTLTextPluginStatus() === 'unavailable'\n  ) {\n    const {pluginUrl, lazy = true} =\n      typeof RTLTextPlugin === 'string' ? {pluginUrl: RTLTextPlugin} : RTLTextPlugin;\n\n    mapLib.setRTLTextPlugin(\n      pluginUrl,\n      (error?: Error) => {\n        if (error) {\n          // eslint-disable-next-line\n          console.error(error);\n        }\n      },\n      lazy\n    );\n  }\n  if (maxParallelImageRequests !== undefined) {\n    mapLib.setMaxParallelImageRequests(maxParallelImageRequests);\n  }\n  if (workerCount !== undefined) {\n    mapLib.setWorkerCount(workerCount);\n  }\n  if (workerUrl !== undefined) {\n    mapLib.setWorkerUrl(workerUrl);\n  }\n}\n", "/* global document */\nimport * as React from 'react';\nimport {createPortal} from 'react-dom';\nimport {useImperativeHandle, useEffect, useMemo, useRef, useContext, forwardRef, memo} from 'react';\nimport {applyReactStyle} from '../utils/apply-react-style';\n\nimport type {PopupInstance, MarkerInstance, MarkerOptions} from '../types/lib';\nimport type {MarkerEvent, MarkerDragEvent} from '../types/events';\n\nimport {MapContext} from './map';\nimport {arePointsEqual} from '../utils/deep-equal';\nimport {compareClassNames} from '../utils/compare-class-names';\n\nexport type MarkerProps = MarkerOptions & {\n  /** Longitude of the anchor location */\n  longitude: number;\n  /** Latitude of the anchor location */\n  latitude: number;\n\n  popup?: PopupInstance;\n\n  /** CSS style override, applied to the control's container */\n  style?: React.CSSProperties;\n  onClick?: (e: MarkerEvent<MouseEvent>) => void;\n  onDragStart?: (e: MarkerDragEvent) => void;\n  onDrag?: (e: MarkerDragEvent) => void;\n  onDragEnd?: (e: MarkerDragEvent) => void;\n  children?: React.ReactNode;\n};\n\n/* eslint-disable complexity,max-statements */\nexport const Marker = memo(\n  forwardRef((props: MarkerProps, ref: React.Ref<MarkerInstance>) => {\n    const {map, mapLib} = useContext(MapContext);\n    const thisRef = useRef({props});\n\n    const marker: MarkerInstance = useMemo(() => {\n      let hasChildren = false;\n      React.Children.forEach(props.children, el => {\n        if (el) {\n          hasChildren = true;\n        }\n      });\n      const options = {\n        ...props,\n        element: hasChildren ? document.createElement('div') : undefined\n      };\n\n      const mk = new mapLib.Marker(options);\n      mk.setLngLat([props.longitude, props.latitude]);\n\n      mk.getElement().addEventListener('click', (e: MouseEvent) => {\n        thisRef.current.props.onClick?.({\n          type: 'click',\n          target: mk,\n          originalEvent: e\n        });\n      });\n\n      mk.on('dragstart', e => {\n        const evt = e as MarkerDragEvent;\n        evt.lngLat = marker.getLngLat();\n        thisRef.current.props.onDragStart?.(evt);\n      });\n      mk.on('drag', e => {\n        const evt = e as MarkerDragEvent;\n        evt.lngLat = marker.getLngLat();\n        thisRef.current.props.onDrag?.(evt);\n      });\n      mk.on('dragend', e => {\n        const evt = e as MarkerDragEvent;\n        evt.lngLat = marker.getLngLat();\n        thisRef.current.props.onDragEnd?.(evt);\n      });\n\n      return mk;\n    }, []);\n\n    useEffect(() => {\n      marker.addTo(map.getMap());\n\n      return () => {\n        marker.remove();\n      };\n    }, []);\n\n    const {\n      longitude,\n      latitude,\n      offset,\n      style,\n      draggable = false,\n      popup = null,\n      rotation = 0,\n      rotationAlignment = 'auto',\n      pitchAlignment = 'auto'\n    } = props;\n\n    useEffect(() => {\n      applyReactStyle(marker.getElement(), style);\n    }, [style]);\n\n    useImperativeHandle(ref, () => marker, []);\n\n    const oldProps = thisRef.current.props;\n    if (marker.getLngLat().lng !== longitude || marker.getLngLat().lat !== latitude) {\n      marker.setLngLat([longitude, latitude]);\n    }\n    if (offset && !arePointsEqual(marker.getOffset(), offset)) {\n      marker.setOffset(offset);\n    }\n    if (marker.isDraggable() !== draggable) {\n      marker.setDraggable(draggable);\n    }\n    if (marker.getRotation() !== rotation) {\n      marker.setRotation(rotation);\n    }\n    if (marker.getRotationAlignment() !== rotationAlignment) {\n      marker.setRotationAlignment(rotationAlignment);\n    }\n    if (marker.getPitchAlignment() !== pitchAlignment) {\n      marker.setPitchAlignment(pitchAlignment);\n    }\n    if (marker.getPopup() !== popup) {\n      marker.setPopup(popup);\n    }\n    const classNameDiff = compareClassNames(oldProps.className, props.className);\n    if (classNameDiff) {\n      for (const c of classNameDiff) {\n        marker.toggleClassName(c);\n      }\n    }\n\n    thisRef.current.props = props;\n    return createPortal(props.children, marker.getElement());\n  })\n);\n", "import * as React from 'react';\n// This is a simplified version of\n// https://github.com/facebook/react/blob/4131af3e4bf52f3a003537ec95a1655147c81270/src/renderers/dom/shared/CSSPropertyOperations.js#L62\nconst unitlessNumber = /box|flex|grid|column|lineHeight|fontWeight|opacity|order|tabSize|zIndex/;\n\nexport function applyReactStyle(element: HTMLElement, styles: React.CSSProperties) {\n  if (!element || !styles) {\n    return;\n  }\n  const style = element.style;\n\n  for (const key in styles) {\n    const value = styles[key];\n    if (Number.isFinite(value) && !unitlessNumber.test(key)) {\n      style[key] = `${value}px`;\n    } else {\n      style[key] = value;\n    }\n  }\n}\n", "/** Compare two classNames string and return the difference */\nexport function compareClassNames(\n  prevClassName: string | undefined,\n  nextClassName: string | undefined\n): string[] | null {\n  if (prevClassName === nextClassName) {\n    return null;\n  }\n\n  const prevClassList = getClassList(prevClassName);\n  const nextClassList = getClassList(nextClassName);\n  const diff: string[] = [];\n\n  for (const c of nextClassList) {\n    if (!prevClassList.has(c)) {\n      diff.push(c);\n    }\n  }\n  for (const c of prevClassList) {\n    if (!nextClassList.has(c)) {\n      diff.push(c);\n    }\n  }\n  return diff.length === 0 ? null : diff;\n}\n\nfunction getClassList(className: string | undefined) {\n  return new Set(className ? className.trim().split(/\\s+/) : []);\n}\n", "/* global document */\nimport * as React from 'react';\nimport {createPortal} from 'react-dom';\nimport {useImperativeHandle, useEffect, useMemo, useRef, useContext, forwardRef, memo} from 'react';\nimport {applyReactStyle} from '../utils/apply-react-style';\n\nimport type {PopupInstance, PopupOptions} from '../types/lib';\nimport type {PopupEvent} from '../types/events';\n\nimport {MapContext} from './map';\nimport {deepEqual} from '../utils/deep-equal';\nimport {compareClassNames} from '../utils/compare-class-names';\n\nexport type PopupProps = PopupOptions & {\n  /** Longitude of the anchor location */\n  longitude: number;\n  /** Latitude of the anchor location */\n  latitude: number;\n\n  /** CSS style override, applied to the control's container */\n  style?: React.CSSProperties;\n\n  onOpen?: (e: PopupEvent) => void;\n  onClose?: (e: PopupEvent) => void;\n  children?: React.ReactNode;\n};\n\n/* eslint-disable complexity,max-statements */\nexport const Popup = memo(\n  forwardRef((props: PopupProps, ref: React.Ref<PopupInstance>) => {\n    const {map, mapLib} = useContext(MapContext);\n    const container = useMemo(() => {\n      return document.createElement('div');\n    }, []);\n    const thisRef = useRef({props});\n\n    const popup: PopupInstance = useMemo(() => {\n      const options = {...props};\n      const pp = new mapLib.Popup(options);\n      pp.setLngLat([props.longitude, props.latitude]);\n      pp.once('open', e => {\n        thisRef.current.props.onOpen?.(e as PopupEvent);\n      });\n      return pp;\n    }, []);\n\n    useEffect(() => {\n      const onClose = e => {\n        thisRef.current.props.onClose?.(e as PopupEvent);\n      };\n      popup.on('close', onClose);\n      popup.setDOMContent(container).addTo(map.getMap());\n\n      return () => {\n        // https://github.com/visgl/react-map-gl/issues/1825\n        // onClose should not be fired if the popup is removed by unmounting\n        // When using React strict mode, the component is mounted twice.\n        // Firing the onClose callback here would be a false signal to remove the component.\n        popup.off('close', onClose);\n        if (popup.isOpen()) {\n          popup.remove();\n        }\n      };\n    }, []);\n\n    useEffect(() => {\n      applyReactStyle(popup.getElement(), props.style);\n    }, [props.style]);\n\n    useImperativeHandle(ref, () => popup, []);\n\n    if (popup.isOpen()) {\n      const oldProps = thisRef.current.props;\n      if (popup.getLngLat().lng !== props.longitude || popup.getLngLat().lat !== props.latitude) {\n        popup.setLngLat([props.longitude, props.latitude]);\n      }\n      if (props.offset && !deepEqual(oldProps.offset, props.offset)) {\n        popup.setOffset(props.offset);\n      }\n      if (oldProps.anchor !== props.anchor || oldProps.maxWidth !== props.maxWidth) {\n        popup.options.anchor = props.anchor;\n        popup.setMaxWidth(props.maxWidth);\n      }\n      const classNameDiff = compareClassNames(oldProps.className, props.className);\n      if (classNameDiff) {\n        for (const c of classNameDiff) {\n          popup.toggleClassName(c);\n        }\n      }\n      thisRef.current.props = props;\n    }\n\n    return createPortal(props.children, container);\n  })\n);\n", "import * as React from 'react';\nimport {useEffect, memo} from 'react';\nimport {applyReactStyle} from '../utils/apply-react-style';\nimport {useControl} from './use-control';\n\nimport type {ControlPosition, AttributionControlOptions} from '../types/lib';\n\nexport type AttributionControlProps = AttributionControlOptions & {\n  /** Placement of the control relative to the map. */\n  position?: ControlPosition;\n  /** CSS style override, applied to the control's container */\n  style?: React.CSSProperties;\n};\n\nfunction _AttributionControl(props: AttributionControlProps) {\n  const ctrl = useControl(({mapLib}) => new mapLib.AttributionControl(props), {\n    position: props.position\n  });\n\n  useEffect(() => {\n    applyReactStyle(ctrl._container, props.style);\n  }, [props.style]);\n\n  return null;\n}\n\nexport const AttributionControl = memo(_AttributionControl);\n", "import {useContext, useMemo, useEffect} from 'react';\nimport type {IControl, ControlPosition} from '../types/lib';\nimport {MapContext} from './map';\nimport type {MapContextValue} from './map';\n\ntype ControlOptions = {\n  position?: ControlPosition;\n};\n\nexport function useControl<T extends IControl>(\n  onCreate: (context: MapContextValue) => T,\n  opts?: ControlOptions\n): T;\n\nexport function useControl<T extends IControl>(\n  onCreate: (context: MapContextValue) => T,\n  onRemove: (context: MapContextValue) => void,\n  opts?: ControlOptions\n): T;\n\nexport function useControl<T extends IControl>(\n  onCreate: (context: MapContextValue) => T,\n  onAdd: (context: MapContextValue) => void,\n  onRemove: (context: MapContextValue) => void,\n  opts?: ControlOptions\n): T;\n\nexport function useControl<T extends IControl>(\n  onCreate: (context: MapContextValue) => T,\n  arg1?: ((context: MapContextValue) => void) | ControlOptions,\n  arg2?: ((context: MapContextValue) => void) | ControlOptions,\n  arg3?: ControlOptions\n): T {\n  const context = useContext(MapContext);\n  const ctrl = useMemo(() => onCreate(context), []);\n\n  useEffect(() => {\n    const opts = (arg3 || arg2 || arg1) as ControlOptions;\n    const onAdd = typeof arg1 === 'function' && typeof arg2 === 'function' ? arg1 : null;\n    const onRemove = typeof arg2 === 'function' ? arg2 : typeof arg1 === 'function' ? arg1 : null;\n\n    const {map} = context;\n    if (!map.hasControl(ctrl)) {\n      map.addControl(ctrl, opts?.position);\n      if (onAdd) {\n        onAdd(context);\n      }\n    }\n\n    return () => {\n      if (onRemove) {\n        onRemove(context);\n      }\n      // Map might have been removed (parent effects are destroyed before child ones)\n      if (map.hasControl(ctrl)) {\n        map.removeControl(ctrl);\n      }\n    };\n  }, []);\n\n  return ctrl;\n}\n", "/* global document */\nimport * as React from 'react';\nimport {useEffect, memo} from 'react';\nimport {applyReactStyle} from '../utils/apply-react-style';\nimport {useControl} from './use-control';\n\nimport type {ControlPosition, FullscreenControlOptions} from '../types/lib';\n\nexport type FullscreenControlProps = Omit<FullscreenControlOptions, 'container'> & {\n  /** Id of the DOM element which should be made full screen. By default, the map container\n   * element will be made full screen. */\n  containerId?: string;\n  /** Placement of the control relative to the map. */\n  position?: ControlPosition;\n  /** CSS style override, applied to the control's container */\n  style?: React.CSSProperties;\n};\n\nfunction _FullscreenControl(props: FullscreenControlProps) {\n  const ctrl = useControl(\n    ({mapLib}) =>\n      new mapLib.FullscreenControl({\n        container: props.containerId && document.getElementById(props.containerId)\n      }),\n    {position: props.position}\n  );\n\n  useEffect(() => {\n    applyReactStyle(ctrl._controlContainer, props.style);\n  }, [props.style]);\n\n  return null;\n}\n\nexport const FullscreenControl = memo(_FullscreenControl);\n", "import * as React from 'react';\nimport {useImperativeHandle, useRef, useEffect, forwardRef, memo} from 'react';\nimport {applyReactStyle} from '../utils/apply-react-style';\nimport {useControl} from './use-control';\n\nimport type {\n  ControlPosition,\n  GeolocateControlInstance,\n  GeolocateControlOptions\n} from '../types/lib';\nimport type {GeolocateEvent, GeolocateResultEvent, GeolocateErrorEvent} from '../types/events';\n\nexport type GeolocateControlProps = GeolocateControlOptions & {\n  /** Placement of the control relative to the map. */\n  position?: ControlPosition;\n  /** CSS style override, applied to the control's container */\n  style?: React.CSSProperties;\n\n  /** Called on each Geolocation API position update that returned as success. */\n  onGeolocate?: (e: GeolocateResultEvent) => void;\n  /** Called on each Geolocation API position update that returned as an error. */\n  onError?: (e: GeolocateErrorEvent) => void;\n  /** Called on each Geolocation API position update that returned as success but user position\n   * is out of map `maxBounds`. */\n  onOutOfMaxBounds?: (e: GeolocateResultEvent) => void;\n  /** Called when the GeolocateControl changes to the active lock state. */\n  onTrackUserLocationStart?: (e: GeolocateEvent) => void;\n  /** Called when the GeolocateControl changes to the background state. */\n  onTrackUserLocationEnd?: (e: GeolocateEvent) => void;\n};\n\nfunction _GeolocateControl(props: GeolocateControlProps, ref: React.Ref<GeolocateControlInstance>) {\n  const thisRef = useRef({props});\n\n  const ctrl = useControl(\n    ({mapLib}) => {\n      const gc = new mapLib.GeolocateControl(props);\n\n      // Hack: fix GeolocateControl reuse\n      // When using React strict mode, the component is mounted twice.\n      // GeolocateControl's UI creation is asynchronous. Removing and adding it back causes the UI to be initialized twice.\n      const setupUI = gc._setupUI;\n      gc._setupUI = () => {\n        if (!gc._container.hasChildNodes()) {\n          setupUI();\n        }\n      };\n\n      gc.on('geolocate', e => {\n        thisRef.current.props.onGeolocate?.(e as GeolocateResultEvent);\n      });\n      gc.on('error', e => {\n        thisRef.current.props.onError?.(e as GeolocateErrorEvent);\n      });\n      gc.on('outofmaxbounds', e => {\n        thisRef.current.props.onOutOfMaxBounds?.(e as GeolocateResultEvent);\n      });\n      gc.on('trackuserlocationstart', e => {\n        thisRef.current.props.onTrackUserLocationStart?.(e as GeolocateEvent);\n      });\n      gc.on('trackuserlocationend', e => {\n        thisRef.current.props.onTrackUserLocationEnd?.(e as GeolocateEvent);\n      });\n\n      return gc;\n    },\n    {position: props.position}\n  );\n\n  thisRef.current.props = props;\n\n  useImperativeHandle(ref, () => ctrl, []);\n\n  useEffect(() => {\n    applyReactStyle(ctrl._container, props.style);\n  }, [props.style]);\n\n  return null;\n}\n\nexport const GeolocateControl = memo(forwardRef(_GeolocateControl));\n", "import * as React from 'react';\nimport {useEffect, memo} from 'react';\nimport {applyReactStyle} from '../utils/apply-react-style';\nimport {useControl} from './use-control';\n\nimport type {ControlPosition, NavigationControlOptions} from '../types/lib';\n\nexport type NavigationControlProps = NavigationControlOptions & {\n  /** Placement of the control relative to the map. */\n  position?: ControlPosition;\n  /** CSS style override, applied to the control's container */\n  style?: React.CSSProperties;\n};\n\nfunction _NavigationControl(props: NavigationControlProps) {\n  const ctrl = useControl(({mapLib}) => new mapLib.NavigationControl(props), {\n    position: props.position\n  });\n\n  useEffect(() => {\n    applyReactStyle(ctrl._container, props.style);\n  }, [props.style]);\n\n  return null;\n}\n\nexport const NavigationControl = memo(_NavigationControl);\n", "import * as React from 'react';\nimport {useEffect, useRef, memo} from 'react';\nimport {applyReactStyle} from '../utils/apply-react-style';\nimport {useControl} from './use-control';\n\nimport type {ControlPosition, ScaleControlOptions} from '../types/lib';\n\nexport type ScaleControlProps = ScaleControlOptions & {\n  // These props will be further constraint by OptionsT\n  unit?: string;\n  maxWidth?: number;\n\n  /** Placement of the control relative to the map. */\n  position?: ControlPosition;\n  /** CSS style override, applied to the control's container */\n  style?: React.CSSProperties;\n};\n\nfunction _ScaleControl(props: ScaleControlProps) {\n  const ctrl = useControl(({mapLib}) => new mapLib.ScaleControl(props), {\n    position: props.position\n  });\n  const propsRef = useRef<ScaleControlProps>(props);\n\n  const prevProps = propsRef.current;\n  propsRef.current = props;\n\n  const {style} = props;\n\n  if (props.maxWidth !== undefined && props.maxWidth !== prevProps.maxWidth) {\n    ctrl.options.maxWidth = props.maxWidth;\n  }\n  if (props.unit !== undefined && props.unit !== prevProps.unit) {\n    ctrl.setUnit(props.unit);\n  }\n\n  useEffect(() => {\n    applyReactStyle(ctrl._container, style);\n  }, [style]);\n\n  return null;\n}\n\nexport const ScaleControl = memo(_ScaleControl);\n", "import * as React from 'react';\nimport {useEffect, memo} from 'react';\nimport {applyReactStyle} from '../utils/apply-react-style';\nimport {useControl} from './use-control';\n\nimport type {ControlPosition} from '../types/lib';\nimport type {TerrainSpecification} from '../types/style-spec';\n\nexport type TerrainControlProps = TerrainSpecification & {\n  /** Placement of the control relative to the map. */\n  position?: ControlPosition;\n  /** CSS style override, applied to the control's container */\n  style?: React.CSSProperties;\n};\n\nfunction _TerrainControl(props: TerrainControlProps) {\n  const ctrl = useControl(({mapLib}) => new mapLib.TerrainControl(props), {\n    position: props.position\n  });\n\n  useEffect(() => {\n    applyReactStyle(ctrl._container, props.style);\n  }, [props.style]);\n\n  return null;\n}\n\nexport const TerrainControl = memo(_TerrainControl);\n", "import * as React from 'react';\nimport {useEffect, memo} from 'react';\nimport {applyReactStyle} from '../utils/apply-react-style';\nimport {useControl} from './use-control';\n\nimport type {ControlPosition, LogoControlOptions} from '../types/lib';\n\nexport type LogoControlProps = LogoControlOptions & {\n  /** Placement of the control relative to the map. */\n  position?: ControlPosition;\n  /** CSS style override, applied to the control's container */\n  style?: React.CSSProperties;\n};\n\nfunction _LogoControl(props: LogoControlProps) {\n  const ctrl = useControl(({mapLib}) => new mapLib.LogoControl(props), {position: props.position});\n\n  useEffect(() => {\n    applyReactStyle(ctrl._container, props.style);\n  }, [props.style]);\n\n  return null;\n}\n\nexport const LogoControl = memo(_LogoControl);\n", "import * as React from 'react';\nimport {useContext, useEffect, useMemo, useState, useRef, cloneElement} from 'react';\nimport {MapContext} from './map';\nimport assert from '../utils/assert';\nimport {deepEqual} from '../utils/deep-equal';\n\nimport type {\n  GeoJSONSourceImplementation,\n  ImageSourceImplementation,\n  AnySourceImplementation\n} from '../types/internal';\nimport type {SourceSpecification} from '../types/style-spec';\nimport type {MapInstance} from '../types/lib';\n\nexport type SourceProps = SourceSpecification & {\n  id?: string;\n  children?: any;\n};\n\nlet sourceCounter = 0;\n\nfunction createSource(map: MapInstance, id: string, props: SourceProps) {\n  // @ts-ignore\n  if (map.style && map.style._loaded) {\n    const options = {...props};\n    delete options.id;\n    delete options.children;\n    // @ts-ignore\n    map.addSource(id, options);\n    return map.getSource(id);\n  }\n  return null;\n}\n\n/* eslint-disable complexity */\nfunction updateSource(source: AnySourceImplementation, props: SourceProps, prevProps: SourceProps) {\n  assert(props.id === prevProps.id, 'source id changed');\n  assert(props.type === prevProps.type, 'source type changed');\n\n  let changedKey = '';\n  let changedKeyCount = 0;\n\n  for (const key in props) {\n    if (key !== 'children' && key !== 'id' && !deepEqual(prevProps[key], props[key])) {\n      changedKey = key;\n      changedKeyCount++;\n    }\n  }\n\n  if (!changedKeyCount) {\n    return;\n  }\n\n  const type = props.type;\n\n  if (type === 'geojson') {\n    (source as GeoJSONSourceImplementation).setData(props.data);\n  } else if (type === 'image') {\n    (source as ImageSourceImplementation).updateImage({\n      url: props.url,\n      coordinates: props.coordinates\n    });\n  } else {\n    switch (changedKey) {\n      case 'coordinates':\n        // @ts-ignore\n        source.setCoordinates?.(props.coordinates);\n        break;\n      case 'url':\n        // @ts-ignore\n        source.setUrl?.(props.url);\n        break;\n      case 'tiles':\n        // @ts-ignore\n        source.setTiles?.(props.tiles);\n        break;\n      default:\n        // eslint-disable-next-line\n        console.warn(`Unable to update <Source> prop: ${changedKey}`);\n    }\n  }\n}\n/* eslint-enable complexity */\n\nexport function Source(props: SourceProps) {\n  const map = useContext(MapContext).map.getMap();\n  const propsRef = useRef(props);\n  const [, setStyleLoaded] = useState(0);\n\n  const id = useMemo(() => props.id || `jsx-source-${sourceCounter++}`, []);\n\n  useEffect(() => {\n    if (map) {\n      /* global setTimeout */\n      const forceUpdate = () => setTimeout(() => setStyleLoaded(version => version + 1), 0);\n      map.on('styledata', forceUpdate);\n      forceUpdate();\n\n      return () => {\n        map.off('styledata', forceUpdate);\n        // @ts-ignore\n        if (map.style && map.style._loaded && map.getSource(id)) {\n          // Parent effects are destroyed before child ones, see\n          // https://github.com/facebook/react/issues/16728\n          // Source can only be removed after all child layers are removed\n          const allLayers = map.getStyle()?.layers;\n          if (allLayers) {\n            for (const layer of allLayers) {\n              // @ts-ignore (2339) source does not exist on all layer types\n              if (layer.source === id) {\n                map.removeLayer(layer.id);\n              }\n            }\n          }\n          map.removeSource(id);\n        }\n      };\n    }\n    return undefined;\n  }, [map]);\n\n  // @ts-ignore\n  let source = map && map.style && map.getSource(id);\n  if (source) {\n    updateSource(source, props, propsRef.current);\n  } else {\n    source = createSource(map, id, props);\n  }\n  propsRef.current = props;\n\n  return (\n    (source &&\n      React.Children.map(\n        props.children,\n        child =>\n          child &&\n          cloneElement(child, {\n            source: id\n          })\n      )) ||\n    null\n  );\n}\n", "export default function assert(condition: any, message: string) {\n  if (!condition) {\n    throw new Error(message);\n  }\n}\n", "import {useContext, useEffect, useMemo, useState, useRef} from 'react';\nimport {MapContext} from './map';\nimport assert from '../utils/assert';\nimport {deepEqual} from '../utils/deep-equal';\n\nimport type {MapInstance, CustomLayerInterface} from '../types/lib';\nimport type {LayerSpecification} from '../types/style-spec';\n\n// Omiting property from a union type, see\n// https://github.com/microsoft/TypeScript/issues/39556#issuecomment-656925230\ntype OptionalId<T> = T extends {id: string} ? Omit<T, 'id'> & {id?: string} : T;\ntype OptionalSource<T> = T extends {source: string} ? Omit<T, 'source'> & {source?: string} : T;\n\nexport type LayerProps = (OptionalSource<OptionalId<LayerSpecification>> | CustomLayerInterface) & {\n  /** If set, the layer will be inserted before the specified layer */\n  beforeId?: string;\n};\n\n/* eslint-disable complexity, max-statements */\nfunction updateLayer(map: MapInstance, id: string, props: LayerProps, prevProps: LayerProps) {\n  assert(props.id === prevProps.id, 'layer id changed');\n  assert(props.type === prevProps.type, 'layer type changed');\n\n  if (props.type === 'custom' || prevProps.type === 'custom') {\n    return;\n  }\n\n  // @ts-ignore filter does not exist in some Layer types\n  const {layout = {}, paint = {}, filter, minzoom, maxzoom, beforeId} = props;\n\n  if (beforeId !== prevProps.beforeId) {\n    map.moveLayer(id, beforeId);\n  }\n  if (layout !== prevProps.layout) {\n    const prevLayout = prevProps.layout || {};\n    for (const key in layout) {\n      if (!deepEqual(layout[key], prevLayout[key])) {\n        map.setLayoutProperty(id, key, layout[key]);\n      }\n    }\n    for (const key in prevLayout) {\n      if (!layout.hasOwnProperty(key)) {\n        map.setLayoutProperty(id, key, undefined);\n      }\n    }\n  }\n  if (paint !== prevProps.paint) {\n    const prevPaint = prevProps.paint || {};\n    for (const key in paint) {\n      if (!deepEqual(paint[key], prevPaint[key])) {\n        map.setPaintProperty(id, key, paint[key]);\n      }\n    }\n    for (const key in prevPaint) {\n      if (!paint.hasOwnProperty(key)) {\n        map.setPaintProperty(id, key, undefined);\n      }\n    }\n  }\n\n  // @ts-ignore filter does not exist in some Layer types\n  if (!deepEqual(filter, prevProps.filter)) {\n    map.setFilter(id, filter);\n  }\n  if (minzoom !== prevProps.minzoom || maxzoom !== prevProps.maxzoom) {\n    map.setLayerZoomRange(id, minzoom, maxzoom);\n  }\n}\n\nfunction createLayer(map: MapInstance, id: string, props: LayerProps) {\n  // @ts-ignore\n  if (map.style && map.style._loaded && (!('source' in props) || map.getSource(props.source))) {\n    const options: LayerProps = {...props, id};\n    delete options.beforeId;\n\n    // @ts-ignore\n    map.addLayer(options, props.beforeId);\n  }\n}\n\n/* eslint-enable complexity, max-statements */\n\nlet layerCounter = 0;\n\nexport function Layer(props: LayerProps) {\n  const map = useContext(MapContext).map.getMap();\n  const propsRef = useRef(props);\n  const [, setStyleLoaded] = useState(0);\n\n  const id = useMemo(() => props.id || `jsx-layer-${layerCounter++}`, []);\n\n  useEffect(() => {\n    if (map) {\n      const forceUpdate = () => setStyleLoaded(version => version + 1);\n      map.on('styledata', forceUpdate);\n      forceUpdate();\n\n      return () => {\n        map.off('styledata', forceUpdate);\n        // @ts-ignore\n        if (map.style && map.style._loaded && map.getLayer(id)) {\n          map.removeLayer(id);\n        }\n      };\n    }\n    return undefined;\n  }, [map]);\n\n  // @ts-ignore\n  const layer = map && map.style && map.getLayer(id);\n  if (layer) {\n    try {\n      updateLayer(map, id, props, propsRef.current);\n    } catch (error) {\n      console.warn(error); // eslint-disable-line\n    }\n  } else {\n    createLayer(map, id, props);\n  }\n\n  // Store last rendered props\n  propsRef.current = props;\n\n  return null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;ACAA,IAAAA,SAAuB;AACvB,IAAAC,gBAAoF;;;ACDpF,YAAuB;AACvB,mBAAyD;AAWlD,IAAM,qBAA2B,oBAAuC,IAAI;AAE5E,IAAM,cAAsD,WAAQ;AACzE,QAAM,CAAC,MAAM,OAAO,QAAI,uBAAiC,CAAA,CAAE;AAE3D,QAAM,iBAAa,0BAAY,CAAC,KAAa,KAAa,cAAa;AACrE,YAAQ,cAAW;AACjB,UAAI,OAAO,WAAW;AACpB,cAAM,IAAI,MAAM,oCAAoC;MACtD;AACA,UAAI,SAAS,EAAE,GAAG;AAChB,cAAM,IAAI,MAAM,mCAAmC,IAAI;MACzD;AACA,aAAO,EAAC,GAAG,UAAU,CAAC,EAAE,GAAG,IAAG;IAChC,CAAC;EACH,GAAG,CAAA,CAAE;AAEL,QAAM,mBAAe,0BAAY,CAAC,KAAa,cAAa;AAC1D,YAAQ,cAAW;AACjB,UAAI,SAAS,EAAE,GAAG;AAChB,cAAM,WAAW,EAAC,GAAG,SAAQ;AAC7B,eAAO,SAAS,EAAE;AAClB,eAAO;MACT;AACA,aAAO;IACT,CAAC;EACH,GAAG,CAAA,CAAE;AAEL,SACE,oBAAC,mBAAmB,UAAQ,EAC1B,OAAO;IACL;IACA;IACA;IACD,GAEA,MAAM,QAAQ;AAGrB;AAOM,SAAU,SAAM;AA1DtB;AA2DE,QAAM,QAAO,kCAAW,kBAAkB,MAA7B,mBAAgC;AAC7C,QAAM,iBAAa,yBAAW,UAAU;AAExC,QAAM,sBAAkB,sBAAQ,MAAK;AACnC,WAAO,EAAC,GAAG,MAAM,SAAS,yCAAY,IAAG;EAC3C,GAAG,CAAC,MAAM,UAAU,CAAC;AAErB,SAAO;AACT;;;AC3DM,SAAU,eAAe,GAAe,GAAa;AACzD,QAAM,KAAK,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,IAAI;AAC/C,QAAM,KAAK,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,IAAI;AAC/C,QAAM,KAAK,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,IAAI;AAC/C,QAAM,KAAK,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,IAAI;AAC/C,SAAO,OAAO,MAAM,OAAO;AAC7B;AASM,SAAU,UAAU,GAAQ,GAAM;AACtC,MAAI,MAAM,GAAG;AACX,WAAO;EACT;AACA,MAAI,CAAC,KAAK,CAAC,GAAG;AACZ,WAAO;EACT;AACA,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,QAAI,CAAC,MAAM,QAAQ,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ;AAC9C,aAAO;IACT;AACA,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG;AAC1B,eAAO;MACT;IACF;AACA,WAAO;EACT,WAAW,MAAM,QAAQ,CAAC,GAAG;AAC3B,WAAO;EACT;AACA,MAAI,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAClD,UAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,UAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,QAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,aAAO;IACT;AACA,eAAW,OAAO,OAAO;AACvB,UAAI,CAAC,EAAE,eAAe,GAAG,GAAG;AAC1B,eAAO;MACT;AACA,UAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG;AAC9B,eAAO;MACT;IACF;AACA,WAAO;EACT;AACA,SAAO;AACT;;;AClDM,SAAU,qBAAqB,IAAiB;AACpD,SAAO;IACL,WAAW,GAAG,OAAO;IACrB,UAAU,GAAG,OAAO;IACpB,MAAM,GAAG;IACT,OAAO,GAAG;IACV,SAAS,GAAG;IACZ,SAAS,GAAG;;AAEhB;AAOM,SAAU,0BAEd,IAEA,OAAoB;AAEpB,QAAM,IAAwB,MAAM,aAAa;AACjD,QAAM,UAAkC,CAAA;AAExC,MACE,eAAe,KACf,cAAc,MACb,EAAE,cAAc,GAAG,OAAO,OAAO,EAAE,aAAa,GAAG,OAAO,MAC3D;AACA,UAAM,SAAS,GAAG,OAAO;AAEzB,YAAQ,SAAS,IAAI,OAAO,EAAE,WAAW,EAAE,QAAQ;EACrD;AACA,MAAI,UAAU,KAAK,EAAE,SAAS,GAAG,MAAM;AACrC,YAAQ,OAAO,EAAE;EACnB;AACA,MAAI,aAAa,KAAK,EAAE,YAAY,GAAG,SAAS;AAC9C,YAAQ,UAAU,EAAE;EACtB;AACA,MAAI,WAAW,KAAK,EAAE,UAAU,GAAG,OAAO;AACxC,YAAQ,QAAQ,EAAE;EACpB;AACA,MAAI,EAAE,WAAW,GAAG,WAAW,CAAC,UAAU,EAAE,SAAS,GAAG,OAAO,GAAG;AAChE,YAAQ,UAAU,EAAE;EACtB;AACA,SAAO;AACT;;;ACtDA,IAAM,WAAW,CAAC,QAAQ,UAAU,gBAAgB,WAAW,WAAW,UAAU,QAAQ;AAKtF,SAAU,eACd,OAAsE;AAEtE,MAAI,CAAC,OAAO;AACV,WAAO;EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AACA,MAAI,UAAU,OAAO;AACnB,YAAQ,MAAM,KAAI;EACpB;AACA,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO;EACT;AACA,QAAM,aAAa,CAAA;AAEnB,aAAW,SAAS,MAAM,QAAQ;AAChC,eAAW,MAAM,EAAE,IAAI;EACzB;AAEA,QAAM,SAAS,MAAM,OAAO,IAAI,WAAQ;AACtC,QAAI,kBAAgC;AAEpC,QAAI,iBAAiB,OAAO;AAC1B,wBAAkB,OAAO,OAAO,CAAA,GAAI,KAAK;AAGzC,aAAO,gBAAgB;IACzB;AAIA,UAAM,WAAW,WAAW,MAAM,GAAG;AACrC,QAAI,UAAU;AACZ,wBAAkB,mBAAmB,OAAO,OAAO,CAAA,GAAI,KAAK;AAE5D,aAAO,gBAAgB;AAEvB,iBAAW,YAAY,UAAU;AAC/B,YAAI,YAAY,UAAU;AACxB,0BAAgB,QAAQ,IAAI,SAAS,QAAQ;QAC/C;MACF;IACF;AAEA,WAAO,mBAAmB;EAC5B,CAAC;AAGD,SAAO,EAAC,GAAG,OAAO,OAAM;AAC1B;;;ACqBA,IAAM,gBAAgB,EAAC,SAAS,GAAG,SAAS,CAAA,GAAI,QAAQ,CAAA,EAAE;AAE1D,IAAM,gBAAgB;EACpB,WAAW;EACX,SAAS;EACT,WAAW;EACX,WAAW;EACX,OAAO;EACP,UAAU;EACV,YAAY;EACZ,YAAY;EACZ,UAAU;EACV,aAAa;EACb,YAAY;EACZ,UAAU;EACV,WAAW;EACX,aAAa;;AAEf,IAAM,eAAe;EACnB,WAAW;EACX,MAAM;EACN,SAAS;EACT,WAAW;EACX,MAAM;EACN,SAAS;EACT,WAAW;EACX,MAAM;EACN,SAAS;EACT,aAAa;EACb,QAAQ;EACR,WAAW;EACX,YAAY;EACZ,OAAO;EACP,UAAU;;AAEZ,IAAM,cAAc;EAClB,OAAO;EACP,cAAc;EACd,YAAY;EACZ,eAAe;EACf,QAAQ;EACR,MAAM;EACN,QAAQ;EACR,MAAM;EACN,QAAQ;EACR,MAAM;EACN,WAAW;EACX,YAAY;EACZ,OAAO;;AAET,IAAM,eAAe;EACnB;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF,IAAM,eAAe;EACnB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAMF,IAAqB,WAArB,MAA6B;EAoB3B,YACE,UACA,OACA,WAAyB;AApBnB,SAAA,OAAoB;AAKpB,SAAA,kBAA2B;AAC3B,SAAA,mBAAwC;AACxC,SAAA,uBAAyC;AACzC,SAAA,mBAKJ,CAAA;AAqUI,SAAA,WAAW,CAAC,MAAe;AAEjC,YAAM,KAAK,KAAK,MAAM,YAAY,EAAE,IAAI,CAAC;AACzC,UAAI,IAAI;AACN,WAAG,CAAC;MACN,WAAW,EAAE,SAAS,SAAS;AAC7B,gBAAQ,MAAO,EAAiB,KAAK;MACvC;IACF;AAEQ,SAAA,iBAAiB,CAAC,MAA2B;AACnD,UAAI,KAAK,iBAAiB;AACxB;MACF;AACA,QAAE,YAAY,KAAK,wBAAwB,qBAAqB,KAAK,KAAK,SAAS;AAEnF,YAAM,KAAK,KAAK,MAAM,aAAa,EAAE,IAAI,CAAC;AAC1C,UAAI,IAAI;AACN,WAAG,CAAC;MACN;IACF;AAEQ,SAAA,kBAAkB,CAAC,OAAqB;AAC9C,UAAI,KAAK,iBAAiB;AACxB,eAAO;MACT;AACA,WAAK,uBAAuB,qBAAqB,EAAE;AACnD,aAAO,0BAA0B,IAAI,KAAK,KAAK;IACjD;AAyCQ,SAAA,kBAAkB,CAAC,MAAoB;AAC7C,UAAI,EAAE,SAAS,eAAe,EAAE,SAAS,YAAY;AACnD,aAAK,aAAa,CAAC;MACrB;AAGA,YAAM,KAAK,KAAK,MAAM,cAAc,EAAE,IAAI,CAAC;AAC3C,UAAI,IAAI;AACN,YAAI,KAAK,MAAM,uBAAuB,EAAE,SAAS,eAAe,EAAE,SAAS,YAAY;AACrF,YAAE,WAAW,KAAK,oBAAoB,KAAK,uBAAuB,EAAE,KAAK;QAC3E;AACA,WAAG,CAAC;AACJ,eAAO,EAAE;MACX;IACF;AA/YE,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,YAAY,SAAS;EAC5B;EAEA,IAAI,MAAG;AACL,WAAO,KAAK;EACd;EAEA,SAAS,OAAoB;AAC3B,UAAM,WAAW,KAAK;AACtB,SAAK,QAAQ;AAEb,UAAM,kBAAkB,KAAK,gBAAgB,OAAO,QAAQ;AAC5D,UAAM,cAAc,KAAK,YAAY,KAAK;AAC1C,UAAM,mBAAmB,KAAK,iBAAiB,KAAK;AACpD,SAAK,aAAa,OAAO,QAAQ;AACjC,SAAK,uBAAuB,KAAK;AACjC,SAAK,gBAAgB,OAAO,QAAQ;AAKpC,QAAI,mBAAmB,eAAgB,oBAAoB,CAAC,KAAK,KAAK,SAAQ,GAAK;AACjF,WAAK,OAAM;IACb;EACF;EAEA,OAAO,MAAM,OAAsB,WAAyB;AAC1D,UAAM,OAAO,SAAS,UAAU,IAAG;AACnC,QAAI,CAAC,MAAM;AACT,aAAO;IACT;AAEA,UAAM,MAAM,KAAK;AAIjB,UAAM,eAAe,IAAI,aAAY;AACrC,cAAU,YAAY,aAAa;AACnC,WAAO,aAAa,WAAW,SAAS,GAAG;AACzC,gBAAU,YAAY,aAAa,WAAW,CAAC,CAAC;IAClD;AAGA,QAAI,aAAa;AAMjB,UAAM,iBAAiB,IAAI;AAC3B,QAAI,gBAAgB;AAClB,qBAAe,WAAU;AACzB,qBAAe,QAAQ,SAAS;IAClC;AAGA,SAAK,SAAS,EAAC,GAAG,OAAO,cAAc,MAAK,CAAC;AAC7C,QAAI,OAAM;AACV,UAAM,EAAC,iBAAgB,IAAI;AAC3B,QAAI,kBAAkB;AACpB,UAAI,iBAAiB,QAAQ;AAC3B,YAAI,UAAU,iBAAiB,QAAQ,EAAC,GAAG,iBAAiB,kBAAkB,UAAU,EAAC,CAAC;MAC5F,OAAO;AACL,aAAK,iBAAiB,gBAAgB;MACxC;IACF;AAGA,QAAI,IAAI,cAAa,GAAI;AACvB,UAAI,KAAK,MAAM;IACjB,OAAO;AACL,UAAI,KAAK,cAAc,MAAM,IAAI,KAAK,MAAM,CAAC;IAC/C;AAIA,QAAI,QAAO;AACX,WAAO;EACT;;EAGQ,YAAY,WAAyB;AAC3C,UAAM,EAAC,MAAK,IAAI;AAChB,UAAM,EAAC,WAAW,cAAa,IAAI;AACnC,UAAM,aAAa;MACjB,GAAG;MACH,GAAG,MAAM;MACT;MACA,OAAO,eAAe,QAAQ;;AAGhC,UAAM,YAAY,WAAW,oBAAoB,WAAW,aAAa;AACzE,WAAO,OAAO,YAAY;MACxB,QAAQ,CAAC,UAAU,aAAa,GAAG,UAAU,YAAY,CAAC;MAC1D,MAAM,UAAU,QAAQ;MACxB,OAAO,UAAU,SAAS;MAC1B,SAAS,UAAU,WAAW;KAC/B;AAED,QAAI,MAAM,IAAI;AAEZ,YAAM,aAAa,kBAAkB,UAAU;AAI/C,wBAAkB,UAAU,aAAa,MAAK;AAE5C,0BAAkB,UAAU,aAAa;AACzC,eAAO,MAAM;MACf;IACF;AAEA,UAAM,MAAM,IAAI,KAAK,UAAU,UAAU;AAEzC,QAAI,UAAU,SAAS;AACrB,UAAI,WAAW,UAAU,OAAO;IAClC;AACA,QAAI,MAAM,QAAQ;AAChB,UAAI,UAAS,EAAG,MAAM,SAAS,MAAM;IACvC;AAGA,QAAI,wBAAwB,KAAK;AACjC,QAAI,GAAG,cAAc,MAAK;AA/S9B;AAiTM,WAAK,mBAAmB;QACtB,OAAO,IAAI,SAAQ;QACnB,KAAK,IAAI,OAAM;;QAEf,aAAY,SAAI,kBAAJ;QACZ,SAAS,IAAI,WAAU;;AAEzB,WAAK,uBAAuB,KAAK,KAAK;IACxC,CAAC;AACD,QAAI,GAAG,cAAc,MAAK;AAExB,WAAK,uBAAuB,KAAK,KAAK;IACxC,CAAC;AACD,eAAW,aAAa,eAAe;AACrC,UAAI,GAAG,WAAW,KAAK,eAAe;IACxC;AACA,eAAW,aAAa,cAAc;AACpC,UAAI,GAAG,WAAW,KAAK,cAAc;IACvC;AACA,eAAW,aAAa,aAAa;AACnC,UAAI,GAAG,WAAW,KAAK,QAAQ;IACjC;AACA,SAAK,OAAO;EACd;;EAGA,UAAO;AAEL,UAAM,YAAY,KAAK,IAAI,aAAY;AACvC,UAAM,WAAW,UAAU,cAAc,qBAAqB;AAC9D,yCAAU;AAEV,aAAS,UAAU,KAAK,IAAI;EAC9B;EAEA,UAAO;AACL,SAAK,KAAK,OAAM;EAClB;;;;EAKA,SAAM;AACJ,UAAM,MAAM,KAAK;AAIjB,QAAI,IAAI,OAAO;AAEb,UAAI,IAAI,QAAQ;AACd,YAAI,OAAO,OAAM;AACjB,YAAI,SAAS;MACf;AAEA,UAAI,QAAO;IACb;EACF;;;;;EAMQ,YAAY,WAAwB;AAE1C,UAAM,EAAC,UAAS,IAAI;AACpB,QAAI,WAAW;AACb,YAAM,MAAM,KAAK;AACjB,UAAI,UAAU,UAAU,IAAI,UAAU,SAAS,UAAU,WAAW,IAAI,UAAU,QAAQ;AACxF,YAAI,OAAM;AACV,eAAO;MACT;IACF;AACA,WAAO;EACT;;;;;;;EAQQ,iBAAiB,WAAwB;AAC/C,UAAM,MAAM,KAAK;AACjB,UAAM,KAAK,IAAI;AACf,UAAM,WAAW,IAAI,SAAQ;AAI7B,QAAI,CAAC,UAAU;AACb,YAAM,UAAU,0BAA0B,IAAI,SAAS;AACvD,UAAI,OAAO,KAAK,OAAO,EAAE,SAAS,GAAG;AACnC,aAAK,kBAAkB;AACvB,YAAI,OAAO,OAAO;AAClB,aAAK,kBAAkB;AACvB,eAAO;MACT;IACF;AAEA,WAAO;EACT;;;;;;EAOQ,gBAAgB,WAA0B,WAAwB;AACxE,UAAM,MAAM,KAAK;AACjB,QAAI,UAAU;AACd,eAAW,YAAY,cAAc;AACnC,UAAI,YAAY,aAAa,CAAC,UAAU,UAAU,QAAQ,GAAG,UAAU,QAAQ,CAAC,GAAG;AACjF,kBAAU;AACV,cAAM,SAAS,IAAI,MAAM,SAAS,CAAC,EAAE,YAAW,IAAK,SAAS,MAAM,CAAC,GAAG;AACxE,yCAAQ,KAAK,KAAK,UAAU,QAAQ;MACtC;IACF;AACA,WAAO;EACT;;EAGQ,aAAa,WAA0B,WAAwB;AACrE,QAAI,UAAU,WAAW,UAAU,QAAQ;AACzC,WAAK,KAAK,UAAS,EAAG,MAAM,SAAS,UAAU,UAAU;IAC3D;AACA,QAAI,UAAU,aAAa,UAAU,UAAU;AAC7C,YAAM,EAAC,WAAW,eAAe,eAAe,KAAI,IAAI;AACxD,YAAM,UAAe;QACnB,MAAM;;AAER,UAAI,8BAA8B,WAAW;AAE3C,gBAAQ,2BAA2B,UAAU;MAC/C;AACA,WAAK,KAAK,SAAS,eAAe,QAAQ,GAAG,OAAO;IACtD;EACF;;;;;;EAOQ,uBAAuB,EAAC,OAAO,YAAY,KAAK,QAAO,GAAgB;AA/bjF;AAgcI,UAAM,MAAM,KAAK;AACjB,UAAM,YAAY,KAAK;AAEvB,QAAI,IAAI,MAAM,SAAS;AACrB,UAAI,SAAS,CAAC,UAAU,OAAO,UAAU,KAAK,GAAG;AAC/C,kBAAU,QAAQ;AAClB,YAAI,SAAS,KAAK;MACpB;AACA,UACE,cACA,CAAC,UAAU,YAAY,UAAU,UAAU,KAC3C,iBAAe,eAAU,eAAV,mBAAsB,OACrC;AACA,kBAAU,aAAa,OAAO,eAAe,WAAW,EAAC,MAAM,WAAU,IAAI;AAE7E,kBAAI,kBAAJ,6BAAoB,UAAU;MAChC;AACA,UAAI,OAAO,CAAC,UAAU,KAAK,UAAU,GAAG,GAAG;AACzC,kBAAU,MAAM;AAChB,YAAI,OAAO,GAAG;MAChB;AACA,UAAI,YAAY,UAAa,CAAC,UAAU,SAAS,UAAU,OAAO,GAAG;AACnE,YAAI,CAAC,WAAW,IAAI,UAAU,QAAQ,MAAM,GAAG;AAC7C,oBAAU,UAAU;AACpB,cAAI,WAAW,OAAO;QACxB;MACF;IACF;EACF;;EAGQ,gBAAgB,WAA0B,WAAwB;AACxE,UAAM,MAAM,KAAK;AACjB,eAAW,YAAY,cAAc;AACnC,YAAM,WAAW,UAAU,QAAQ,KAAK;AACxC,YAAM,WAAW,UAAU,QAAQ,KAAK;AACxC,UAAI,CAAC,UAAU,UAAU,QAAQ,GAAG;AAClC,YAAI,UAAU;AACZ,cAAI,QAAQ,EAAE,OAAO,QAAQ;QAC/B,OAAO;AACL,cAAI,QAAQ,EAAE,QAAO;QACvB;MACF;IACF;EACF;EAgCQ,uBAAuB,OAAY;AACzC,UAAM,MAAM,KAAK;AACjB,UAAM,EAAC,sBAAsB,CAAA,EAAE,IAAI,KAAK;AACxC,QAAI;AACF,aAAO,IAAI,sBAAsB,OAAO;QACtC,QAAQ,oBAAoB,OAAO,IAAI,SAAS,KAAK,GAAG,CAAC;OAC1D;IACH,QAAE;AAEA,aAAO,CAAA;IACT;EACF;EAEQ,aAAa,GAAgB;AAzhBvC;AA0hBI,UAAM,EAAC,MAAK,IAAI;AAChB,UAAM,6BACJ,MAAM,wBAAwB,MAAM,eAAe,MAAM,gBAAgB,MAAM;AAEjF,QAAI,4BAA4B;AAC9B,YAAM,YAAY,EAAE;AACpB,YAAM,gBAAc,UAAK,qBAAL,mBAAuB,UAAS;AACpD,YAAM,WAAW,KAAK,uBAAuB,EAAE,KAAK;AACpD,YAAM,aAAa,SAAS,SAAS;AAErC,UAAI,CAAC,cAAc,aAAa;AAC9B,UAAE,OAAO;AACT,aAAK,gBAAgB,CAAC;MACxB;AACA,WAAK,mBAAmB;AACxB,UAAI,cAAc,CAAC,aAAa;AAC9B,UAAE,OAAO;AACT,aAAK,gBAAgB,CAAC;MACxB;AACA,QAAE,OAAO;IACX,OAAO;AACL,WAAK,mBAAmB;IAC1B;EACF;;AAtYO,SAAA,YAAwB,CAAA;uBAlBZ;;;ACrJrB,IAAM,cAAc;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAOY,SAAP,UAA2B,aAAqB;AACrD,MAAI,CAAC,aAAa;AAChB,WAAO;EACT;AAEA,QAAM,MAAM,YAAY;AACxB,QAAM,SAAc;IAClB,QAAQ,MAAM;;AAGhB,aAAW,OAAO,eAAe,GAAG,GAAG;AAErC,QAAI,EAAE,OAAO,WAAW,CAAC,YAAY,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,GAAG,EAAE,KAAK,GAAG;IACjC;EACF;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,KAAW;AACjC,QAAM,SAAS,oBAAI,IAAG;AAEtB,MAAI,QAAQ;AACZ,SAAO,OAAO;AACZ,eAAW,OAAO,OAAO,oBAAoB,KAAK,GAAG;AACnD,UACE,IAAI,CAAC,MAAM,OACX,OAAO,IAAI,GAAG,MAAM,cACpB,QAAQ,UACR,QAAQ,oBACR;AACA,eAAO,IAAI,GAAG;MAChB;IACF;AACA,YAAQ,OAAO,eAAe,KAAK;EACrC;AACA,SAAO,MAAM,KAAK,MAAM;AAC1B;;;ACnEA,IAAAC,gBAAyC;AAEzC,IAAM,4BAA4B,OAAO,aAAa,cAAc,gCAAkB;AAEtF,IAAA,uCAAe;;;ACWD,SAAP,WAA4B,QAAa,OAAqB;AACnE,QAAM,EAAC,eAAe,0BAA0B,aAAa,UAAS,IAAI;AAC1E,MACE,iBACA,OAAO,0BACP,OAAO,uBAAsB,MAAO,eACpC;AACA,UAAM,EAAC,WAAW,OAAO,KAAI,IAC3B,OAAO,kBAAkB,WAAW,EAAC,WAAW,cAAa,IAAI;AAEnE,WAAO,iBACL,WACA,CAAC,UAAiB;AAChB,UAAI,OAAO;AAET,gBAAQ,MAAM,KAAK;MACrB;IACF,GACA,IAAI;EAER;AACA,MAAI,6BAA6B,QAAW;AAC1C,WAAO,4BAA4B,wBAAwB;EAC7D;AACA,MAAI,gBAAgB,QAAW;AAC7B,WAAO,eAAe,WAAW;EACnC;AACA,MAAI,cAAc,QAAW;AAC3B,WAAO,aAAa,SAAS;EAC/B;AACF;;;AR9BO,IAAM,aAAmB,qBAA+B,IAAI;AAmBnE,SAAS,KAAK,OAAiB,KAAsB;AACnD,QAAM,yBAAqB,0BAAW,kBAAkB;AACxD,QAAM,CAAC,aAAa,cAAc,QAAI,wBAAmB,IAAI;AAC7D,QAAM,mBAAe,sBAAM;AAE3B,QAAM,EAAC,SAAS,aAAY,QAAI,sBAAwB,EAAC,QAAQ,MAAM,KAAK,KAAI,CAAC;AAEjF,+BAAU,MAAK;AACb,UAAM,SAAS,MAAM;AACrB,QAAI,YAAY;AAChB,QAAI;AAEJ,YAAQ,QAAQ,UAAU,OAAO,aAAa,CAAC,EAC5C,KAAK,CAACC,YAAsC;AAC3C,UAAI,CAAC,WAAW;AACd;MACF;AACA,UAAI,CAACA,SAAQ;AACX,cAAM,IAAI,MAAM,gBAAgB;MAClC;AACA,YAAM,WAAW,SAASA,UAASA,UAASA,QAAO;AACnD,UAAI,CAAC,SAAS,KAAK;AACjB,cAAM,IAAI,MAAM,gBAAgB;MAClC;AAEA,iBAAW,UAAU,KAAK;AAC1B,UAAI,MAAM,WAAW;AACnB,mBAAW,iBAAS,MAAM,OAAO,aAAa,OAAO;MACvD;AACA,UAAI,CAAC,UAAU;AACb,mBAAW,IAAI,iBAAS,SAAS,KAAK,OAAO,aAAa,OAAO;MACnE;AACA,mBAAa,MAAM,UAAU,QAAQ;AACrC,mBAAa,SAAS;AAEtB,qBAAe,QAAQ;AACvB,+DAAoB,WAAW,aAAa,KAAK,MAAM;IACzD,CAAC,EACA,MAAM,WAAQ;AACb,YAAM,EAAC,QAAO,IAAI;AAClB,UAAI,SAAS;AACX,gBAAQ;UACN,MAAM;UACN,QAAQ;UACR,eAAe;UACf;SACD;MACH,OAAO;AACL,gBAAQ,MAAM,KAAK;MACrB;IACF,CAAC;AAEH,WAAO,MAAK;AACV,kBAAY;AACZ,UAAI,UAAU;AACZ,iEAAoB,aAAa,MAAM;AACvC,YAAI,MAAM,WAAW;AACnB,mBAAS,QAAO;QAClB,OAAO;AACL,mBAAS,QAAO;QAClB;MACF;IACF;EACF,GAAG,CAAA,CAAE;AAEL,uCAA0B,MAAK;AAC7B,QAAI,aAAa;AACf,kBAAY,SAAS,KAAK;IAC5B;EACF,CAAC;AAED,yCAAoB,KAAK,MAAM,aAAa,KAAK,CAAC,WAAW,CAAC;AAE9D,QAAM,YAAuB,uBAC3B,OAAO;IACL,UAAU;IACV,OAAO;IACP,QAAQ;IACR,GAAG,MAAM;MAEX,CAAC,MAAM,KAAK,CAAC;AAGf,QAAM,wBAAwB;IAC5B,QAAQ;;AAGV,SACE,qBAAA,OAAA,EAAK,IAAI,MAAM,IAAI,KAAK,cAAc,MAAY,GAC/C,eACC;IAAC,WAAW;IAAQ,EAAC,OAAO,aAAY;IACtC,qBAAA,OAAA,EAAA,qBAAuB,IAAG,OAAO,sBAAqB,GACnD,MAAM,QAAQ;EACX,CAET;AAGP;AAEO,IAAM,MAAY,kBAAW,IAAI;;;ASvIxC,IAAAC,SAAuB;AACvB,uBAA2B;AAC3B,IAAAC,gBAA4F;;;ACA5F,IAAM,iBAAiB;AAEjB,SAAU,gBAAgB,SAAsB,QAA2B;AAC/E,MAAI,CAAC,WAAW,CAAC,QAAQ;AACvB;EACF;AACA,QAAM,QAAQ,QAAQ;AAEtB,aAAW,OAAO,QAAQ;AACxB,UAAM,QAAQ,OAAO,GAAG;AACxB,QAAI,OAAO,SAAS,KAAK,KAAK,CAAC,eAAe,KAAK,GAAG,GAAG;AACvD,YAAM,GAAG,IAAI,GAAG;IAClB,OAAO;AACL,YAAM,GAAG,IAAI;IACf;EACF;AACF;;;AClBM,SAAU,kBACd,eACA,eAAiC;AAEjC,MAAI,kBAAkB,eAAe;AACnC,WAAO;EACT;AAEA,QAAM,gBAAgB,aAAa,aAAa;AAChD,QAAM,gBAAgB,aAAa,aAAa;AAChD,QAAM,OAAiB,CAAA;AAEvB,aAAW,KAAK,eAAe;AAC7B,QAAI,CAAC,cAAc,IAAI,CAAC,GAAG;AACzB,WAAK,KAAK,CAAC;IACb;EACF;AACA,aAAW,KAAK,eAAe;AAC7B,QAAI,CAAC,cAAc,IAAI,CAAC,GAAG;AACzB,WAAK,KAAK,CAAC;IACb;EACF;AACA,SAAO,KAAK,WAAW,IAAI,OAAO;AACpC;AAEA,SAAS,aAAa,WAA6B;AACjD,SAAO,IAAI,IAAI,YAAY,UAAU,KAAI,EAAG,MAAM,KAAK,IAAI,CAAA,CAAE;AAC/D;;;AFGO,IAAM,aAAS,wBACpB,0BAAW,CAAC,OAAoB,QAAkC;AAChE,QAAM,EAAC,KAAK,OAAM,QAAI,0BAAW,UAAU;AAC3C,QAAM,cAAU,sBAAO,EAAC,MAAK,CAAC;AAE9B,QAAM,aAAyB,uBAAQ,MAAK;AAC1C,QAAI,cAAc;AAClB,IAAM,gBAAS,QAAQ,MAAM,UAAU,QAAK;AAC1C,UAAI,IAAI;AACN,sBAAc;MAChB;IACF,CAAC;AACD,UAAM,UAAU;MACd,GAAG;MACH,SAAS,cAAc,SAAS,cAAc,KAAK,IAAI;;AAGzD,UAAM,KAAK,IAAI,OAAO,OAAO,OAAO;AACpC,OAAG,UAAU,CAAC,MAAM,WAAW,MAAM,QAAQ,CAAC;AAE9C,OAAG,WAAU,EAAG,iBAAiB,SAAS,CAAC,MAAiB;AAnDlE;AAoDQ,0BAAQ,QAAQ,OAAM,YAAtB,4BAAgC;QAC9B,MAAM;QACN,QAAQ;QACR,eAAe;;IAEnB,CAAC;AAED,OAAG,GAAG,aAAa,OAAI;AA3D7B;AA4DQ,YAAM,MAAM;AACZ,UAAI,SAAS,OAAO,UAAS;AAC7B,0BAAQ,QAAQ,OAAM,gBAAtB,4BAAoC;IACtC,CAAC;AACD,OAAG,GAAG,QAAQ,OAAI;AAhExB;AAiEQ,YAAM,MAAM;AACZ,UAAI,SAAS,OAAO,UAAS;AAC7B,0BAAQ,QAAQ,OAAM,WAAtB,4BAA+B;IACjC,CAAC;AACD,OAAG,GAAG,WAAW,OAAI;AArE3B;AAsEQ,YAAM,MAAM;AACZ,UAAI,SAAS,OAAO,UAAS;AAC7B,0BAAQ,QAAQ,OAAM,cAAtB,4BAAkC;IACpC,CAAC;AAED,WAAO;EACT,GAAG,CAAA,CAAE;AAEL,+BAAU,MAAK;AACb,WAAO,MAAM,IAAI,OAAM,CAAE;AAEzB,WAAO,MAAK;AACV,aAAO,OAAM;IACf;EACF,GAAG,CAAA,CAAE;AAEL,QAAM,EACJ,WACA,UACA,QACA,OACA,YAAY,OACZ,QAAQ,MACR,WAAW,GACX,oBAAoB,QACpB,iBAAiB,OAAM,IACrB;AAEJ,+BAAU,MAAK;AACb,oBAAgB,OAAO,WAAU,GAAI,KAAK;EAC5C,GAAG,CAAC,KAAK,CAAC;AAEV,yCAAoB,KAAK,MAAM,QAAQ,CAAA,CAAE;AAEzC,QAAM,WAAW,QAAQ,QAAQ;AACjC,MAAI,OAAO,UAAS,EAAG,QAAQ,aAAa,OAAO,UAAS,EAAG,QAAQ,UAAU;AAC/E,WAAO,UAAU,CAAC,WAAW,QAAQ,CAAC;EACxC;AACA,MAAI,UAAU,CAAC,eAAe,OAAO,UAAS,GAAI,MAAM,GAAG;AACzD,WAAO,UAAU,MAAM;EACzB;AACA,MAAI,OAAO,YAAW,MAAO,WAAW;AACtC,WAAO,aAAa,SAAS;EAC/B;AACA,MAAI,OAAO,YAAW,MAAO,UAAU;AACrC,WAAO,YAAY,QAAQ;EAC7B;AACA,MAAI,OAAO,qBAAoB,MAAO,mBAAmB;AACvD,WAAO,qBAAqB,iBAAiB;EAC/C;AACA,MAAI,OAAO,kBAAiB,MAAO,gBAAgB;AACjD,WAAO,kBAAkB,cAAc;EACzC;AACA,MAAI,OAAO,SAAQ,MAAO,OAAO;AAC/B,WAAO,SAAS,KAAK;EACvB;AACA,QAAM,gBAAgB,kBAAkB,SAAS,WAAW,MAAM,SAAS;AAC3E,MAAI,eAAe;AACjB,eAAW,KAAK,eAAe;AAC7B,aAAO,gBAAgB,CAAC;IAC1B;EACF;AAEA,UAAQ,QAAQ,QAAQ;AACxB,aAAO,+BAAa,MAAM,UAAU,OAAO,WAAU,CAAE;AACzD,CAAC,CAAC;;;AGrIJ,IAAAC,oBAA2B;AAC3B,IAAAC,gBAA4F;AAyBrF,IAAM,YAAQ,wBACnB,0BAAW,CAAC,OAAmB,QAAiC;AAC9D,QAAM,EAAC,KAAK,OAAM,QAAI,0BAAW,UAAU;AAC3C,QAAM,gBAAY,uBAAQ,MAAK;AAC7B,WAAO,SAAS,cAAc,KAAK;EACrC,GAAG,CAAA,CAAE;AACL,QAAM,cAAU,sBAAO,EAAC,MAAK,CAAC;AAE9B,QAAM,YAAuB,uBAAQ,MAAK;AACxC,UAAM,UAAU,EAAC,GAAG,MAAK;AACzB,UAAM,KAAK,IAAI,OAAO,MAAM,OAAO;AACnC,OAAG,UAAU,CAAC,MAAM,WAAW,MAAM,QAAQ,CAAC;AAC9C,OAAG,KAAK,QAAQ,OAAI;AAtC1B;AAuCQ,0BAAQ,QAAQ,OAAM,WAAtB,4BAA+B;IACjC,CAAC;AACD,WAAO;EACT,GAAG,CAAA,CAAE;AAEL,+BAAU,MAAK;AACb,UAAM,UAAU,OAAI;AA7C1B;AA8CQ,0BAAQ,QAAQ,OAAM,YAAtB,4BAAgC;IAClC;AACA,UAAM,GAAG,SAAS,OAAO;AACzB,UAAM,cAAc,SAAS,EAAE,MAAM,IAAI,OAAM,CAAE;AAEjD,WAAO,MAAK;AAKV,YAAM,IAAI,SAAS,OAAO;AAC1B,UAAI,MAAM,OAAM,GAAI;AAClB,cAAM,OAAM;MACd;IACF;EACF,GAAG,CAAA,CAAE;AAEL,+BAAU,MAAK;AACb,oBAAgB,MAAM,WAAU,GAAI,MAAM,KAAK;EACjD,GAAG,CAAC,MAAM,KAAK,CAAC;AAEhB,yCAAoB,KAAK,MAAM,OAAO,CAAA,CAAE;AAExC,MAAI,MAAM,OAAM,GAAI;AAClB,UAAM,WAAW,QAAQ,QAAQ;AACjC,QAAI,MAAM,UAAS,EAAG,QAAQ,MAAM,aAAa,MAAM,UAAS,EAAG,QAAQ,MAAM,UAAU;AACzF,YAAM,UAAU,CAAC,MAAM,WAAW,MAAM,QAAQ,CAAC;IACnD;AACA,QAAI,MAAM,UAAU,CAAC,UAAU,SAAS,QAAQ,MAAM,MAAM,GAAG;AAC7D,YAAM,UAAU,MAAM,MAAM;IAC9B;AACA,QAAI,SAAS,WAAW,MAAM,UAAU,SAAS,aAAa,MAAM,UAAU;AAC5E,YAAM,QAAQ,SAAS,MAAM;AAC7B,YAAM,YAAY,MAAM,QAAQ;IAClC;AACA,UAAM,gBAAgB,kBAAkB,SAAS,WAAW,MAAM,SAAS;AAC3E,QAAI,eAAe;AACjB,iBAAW,KAAK,eAAe;AAC7B,cAAM,gBAAgB,CAAC;MACzB;IACF;AACA,YAAQ,QAAQ,QAAQ;EAC1B;AAEA,aAAO,gCAAa,MAAM,UAAU,SAAS;AAC/C,CAAC,CAAC;;;AC5FJ,IAAAC,gBAA8B;;;ACD9B,IAAAC,gBAA6C;AA2BvC,SAAU,WACd,UACA,MACA,MACA,MAAqB;AAErB,QAAM,cAAU,0BAAW,UAAU;AACrC,QAAM,WAAO,uBAAQ,MAAM,SAAS,OAAO,GAAG,CAAA,CAAE;AAEhD,+BAAU,MAAK;AACb,UAAM,OAAQ,QAAQ,QAAQ;AAC9B,UAAM,QAAQ,OAAO,SAAS,cAAc,OAAO,SAAS,aAAa,OAAO;AAChF,UAAM,WAAW,OAAO,SAAS,aAAa,OAAO,OAAO,SAAS,aAAa,OAAO;AAEzF,UAAM,EAAC,IAAG,IAAI;AACd,QAAI,CAAC,IAAI,WAAW,IAAI,GAAG;AACzB,UAAI,WAAW,MAAM,6BAAM,QAAQ;AACnC,UAAI,OAAO;AACT,cAAM,OAAO;MACf;IACF;AAEA,WAAO,MAAK;AACV,UAAI,UAAU;AACZ,iBAAS,OAAO;MAClB;AAEA,UAAI,IAAI,WAAW,IAAI,GAAG;AACxB,YAAI,cAAc,IAAI;MACxB;IACF;EACF,GAAG,CAAA,CAAE;AAEL,SAAO;AACT;;;AD/CA,SAAS,oBAAoB,OAA8B;AACzD,QAAM,OAAO,WAAW,CAAC,EAAC,OAAM,MAAM,IAAI,OAAO,mBAAmB,KAAK,GAAG;IAC1E,UAAU,MAAM;GACjB;AAED,+BAAU,MAAK;AACb,oBAAgB,KAAK,YAAY,MAAM,KAAK;EAC9C,GAAG,CAAC,MAAM,KAAK,CAAC;AAEhB,SAAO;AACT;AAEO,IAAM,yBAAqB,oBAAK,mBAAmB;;;AExB1D,IAAAC,gBAA8B;AAgB9B,SAAS,mBAAmB,OAA6B;AACvD,QAAM,OAAO,WACX,CAAC,EAAC,OAAM,MACN,IAAI,OAAO,kBAAkB;IAC3B,WAAW,MAAM,eAAe,SAAS,eAAe,MAAM,WAAW;GAC1E,GACH,EAAC,UAAU,MAAM,SAAQ,CAAC;AAG5B,+BAAU,MAAK;AACb,oBAAgB,KAAK,mBAAmB,MAAM,KAAK;EACrD,GAAG,CAAC,MAAM,KAAK,CAAC;AAEhB,SAAO;AACT;AAEO,IAAM,wBAAoB,oBAAK,kBAAkB;;;ACjCxD,IAAAC,gBAAuE;AA8BvE,SAAS,kBAAkB,OAA8B,KAAwC;AAC/F,QAAM,cAAU,sBAAO,EAAC,MAAK,CAAC;AAE9B,QAAM,OAAO,WACX,CAAC,EAAC,OAAM,MAAK;AACX,UAAM,KAAK,IAAI,OAAO,iBAAiB,KAAK;AAK5C,UAAM,UAAU,GAAG;AACnB,OAAG,WAAW,MAAK;AACjB,UAAI,CAAC,GAAG,WAAW,cAAa,GAAI;AAClC,gBAAO;MACT;IACF;AAEA,OAAG,GAAG,aAAa,OAAI;AA/C7B;AAgDQ,0BAAQ,QAAQ,OAAM,gBAAtB,4BAAoC;IACtC,CAAC;AACD,OAAG,GAAG,SAAS,OAAI;AAlDzB;AAmDQ,0BAAQ,QAAQ,OAAM,YAAtB,4BAAgC;IAClC,CAAC;AACD,OAAG,GAAG,kBAAkB,OAAI;AArDlC;AAsDQ,0BAAQ,QAAQ,OAAM,qBAAtB,4BAAyC;IAC3C,CAAC;AACD,OAAG,GAAG,0BAA0B,OAAI;AAxD1C;AAyDQ,0BAAQ,QAAQ,OAAM,6BAAtB,4BAAiD;IACnD,CAAC;AACD,OAAG,GAAG,wBAAwB,OAAI;AA3DxC;AA4DQ,0BAAQ,QAAQ,OAAM,2BAAtB,4BAA+C;IACjD,CAAC;AAED,WAAO;EACT,GACA,EAAC,UAAU,MAAM,SAAQ,CAAC;AAG5B,UAAQ,QAAQ,QAAQ;AAExB,yCAAoB,KAAK,MAAM,MAAM,CAAA,CAAE;AAEvC,+BAAU,MAAK;AACb,oBAAgB,KAAK,YAAY,MAAM,KAAK;EAC9C,GAAG,CAAC,MAAM,KAAK,CAAC;AAEhB,SAAO;AACT;AAEO,IAAM,uBAAmB,wBAAK,0BAAW,iBAAiB,CAAC;;;AC/ElE,IAAAC,iBAA8B;AAa9B,SAAS,mBAAmB,OAA6B;AACvD,QAAM,OAAO,WAAW,CAAC,EAAC,OAAM,MAAM,IAAI,OAAO,kBAAkB,KAAK,GAAG;IACzE,UAAU,MAAM;GACjB;AAED,gCAAU,MAAK;AACb,oBAAgB,KAAK,YAAY,MAAM,KAAK;EAC9C,GAAG,CAAC,MAAM,KAAK,CAAC;AAEhB,SAAO;AACT;AAEO,IAAM,wBAAoB,qBAAK,kBAAkB;;;ACzBxD,IAAAC,iBAAsC;AAiBtC,SAAS,cAAc,OAAwB;AAC7C,QAAM,OAAO,WAAW,CAAC,EAAC,OAAM,MAAM,IAAI,OAAO,aAAa,KAAK,GAAG;IACpE,UAAU,MAAM;GACjB;AACD,QAAM,eAAW,uBAA0B,KAAK;AAEhD,QAAM,YAAY,SAAS;AAC3B,WAAS,UAAU;AAEnB,QAAM,EAAC,MAAK,IAAI;AAEhB,MAAI,MAAM,aAAa,UAAa,MAAM,aAAa,UAAU,UAAU;AACzE,SAAK,QAAQ,WAAW,MAAM;EAChC;AACA,MAAI,MAAM,SAAS,UAAa,MAAM,SAAS,UAAU,MAAM;AAC7D,SAAK,QAAQ,MAAM,IAAI;EACzB;AAEA,gCAAU,MAAK;AACb,oBAAgB,KAAK,YAAY,KAAK;EACxC,GAAG,CAAC,KAAK,CAAC;AAEV,SAAO;AACT;AAEO,IAAM,mBAAe,qBAAK,aAAa;;;AC1C9C,IAAAC,iBAA8B;AAc9B,SAAS,gBAAgB,OAA0B;AACjD,QAAM,OAAO,WAAW,CAAC,EAAC,OAAM,MAAM,IAAI,OAAO,eAAe,KAAK,GAAG;IACtE,UAAU,MAAM;GACjB;AAED,gCAAU,MAAK;AACb,oBAAgB,KAAK,YAAY,MAAM,KAAK;EAC9C,GAAG,CAAC,MAAM,KAAK,CAAC;AAEhB,SAAO;AACT;AAEO,IAAM,qBAAiB,qBAAK,eAAe;;;AC1BlD,IAAAC,iBAA8B;AAa9B,SAAS,aAAa,OAAuB;AAC3C,QAAM,OAAO,WAAW,CAAC,EAAC,OAAM,MAAM,IAAI,OAAO,YAAY,KAAK,GAAG,EAAC,UAAU,MAAM,SAAQ,CAAC;AAE/F,gCAAU,MAAK;AACb,oBAAgB,KAAK,YAAY,MAAM,KAAK;EAC9C,GAAG,CAAC,MAAM,KAAK,CAAC;AAEhB,SAAO;AACT;AAEO,IAAM,kBAAc,qBAAK,YAAY;;;ACxB5C,IAAAC,SAAuB;AACvB,IAAAC,iBAA6E;;;ACD/D,SAAP,OAAwB,WAAgB,SAAe;AAC5D,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,OAAO;EACzB;AACF;;;ADeA,IAAI,gBAAgB;AAEpB,SAAS,aAAa,KAAkB,IAAY,OAAkB;AAEpE,MAAI,IAAI,SAAS,IAAI,MAAM,SAAS;AAClC,UAAM,UAAU,EAAC,GAAG,MAAK;AACzB,WAAO,QAAQ;AACf,WAAO,QAAQ;AAEf,QAAI,UAAU,IAAI,OAAO;AACzB,WAAO,IAAI,UAAU,EAAE;EACzB;AACA,SAAO;AACT;AAGA,SAAS,aAAa,QAAiC,OAAoB,WAAsB;AAnCjG;AAoCE,SAAO,MAAM,OAAO,UAAU,IAAI,mBAAmB;AACrD,SAAO,MAAM,SAAS,UAAU,MAAM,qBAAqB;AAE3D,MAAI,aAAa;AACjB,MAAI,kBAAkB;AAEtB,aAAW,OAAO,OAAO;AACvB,QAAI,QAAQ,cAAc,QAAQ,QAAQ,CAAC,UAAU,UAAU,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG;AAChF,mBAAa;AACb;IACF;EACF;AAEA,MAAI,CAAC,iBAAiB;AACpB;EACF;AAEA,QAAM,OAAO,MAAM;AAEnB,MAAI,SAAS,WAAW;AACrB,WAAuC,QAAQ,MAAM,IAAI;EAC5D,WAAW,SAAS,SAAS;AAC1B,WAAqC,YAAY;MAChD,KAAK,MAAM;MACX,aAAa,MAAM;KACpB;EACH,OAAO;AACL,YAAQ,YAAY;MAClB,KAAK;AAEH,qBAAO,mBAAP,gCAAwB,MAAM;AAC9B;MACF,KAAK;AAEH,qBAAO,WAAP,gCAAgB,MAAM;AACtB;MACF,KAAK;AAEH,qBAAO,aAAP,gCAAkB,MAAM;AACxB;MACF;AAEE,gBAAQ,KAAK,mCAAmC,YAAY;IAChE;EACF;AACF;AAGM,SAAU,OAAO,OAAkB;AACvC,QAAM,UAAM,2BAAW,UAAU,EAAE,IAAI,OAAM;AAC7C,QAAM,eAAW,uBAAO,KAAK;AAC7B,QAAM,CAAC,EAAE,cAAc,QAAI,yBAAS,CAAC;AAErC,QAAM,SAAK,wBAAQ,MAAM,MAAM,MAAM,cAAc,mBAAmB,CAAA,CAAE;AAExE,gCAAU,MAAK;AACb,QAAI,KAAK;AAEP,YAAM,cAAc,MAAM,WAAW,MAAM,eAAe,aAAW,UAAU,CAAC,GAAG,CAAC;AACpF,UAAI,GAAG,aAAa,WAAW;AAC/B,kBAAW;AAEX,aAAO,MAAK;AAlGlB;AAmGQ,YAAI,IAAI,aAAa,WAAW;AAEhC,YAAI,IAAI,SAAS,IAAI,MAAM,WAAW,IAAI,UAAU,EAAE,GAAG;AAIvD,gBAAM,aAAY,SAAI,SAAQ,MAAZ,mBAAgB;AAClC,cAAI,WAAW;AACb,uBAAW,SAAS,WAAW;AAE7B,kBAAI,MAAM,WAAW,IAAI;AACvB,oBAAI,YAAY,MAAM,EAAE;cAC1B;YACF;UACF;AACA,cAAI,aAAa,EAAE;QACrB;MACF;IACF;AACA,WAAO;EACT,GAAG,CAAC,GAAG,CAAC;AAGR,MAAI,SAAS,OAAO,IAAI,SAAS,IAAI,UAAU,EAAE;AACjD,MAAI,QAAQ;AACV,iBAAa,QAAQ,OAAO,SAAS,OAAO;EAC9C,OAAO;AACL,aAAS,aAAa,KAAK,IAAI,KAAK;EACtC;AACA,WAAS,UAAU;AAEnB,SACG,UACO,gBAAS,IACb,MAAM,UACN,WACE,aACA,6BAAa,OAAO;IAClB,QAAQ;GACT,CAAC,KAER;AAEJ;;;AE9IA,IAAAC,iBAA+D;AAmB/D,SAAS,YAAY,KAAkB,IAAY,OAAmB,WAAqB;AACzF,SAAO,MAAM,OAAO,UAAU,IAAI,kBAAkB;AACpD,SAAO,MAAM,SAAS,UAAU,MAAM,oBAAoB;AAE1D,MAAI,MAAM,SAAS,YAAY,UAAU,SAAS,UAAU;AAC1D;EACF;AAGA,QAAM,EAAC,SAAS,CAAA,GAAI,QAAQ,CAAA,GAAI,QAAQ,SAAS,SAAS,SAAQ,IAAI;AAEtE,MAAI,aAAa,UAAU,UAAU;AACnC,QAAI,UAAU,IAAI,QAAQ;EAC5B;AACA,MAAI,WAAW,UAAU,QAAQ;AAC/B,UAAM,aAAa,UAAU,UAAU,CAAA;AACvC,eAAW,OAAO,QAAQ;AACxB,UAAI,CAAC,UAAU,OAAO,GAAG,GAAG,WAAW,GAAG,CAAC,GAAG;AAC5C,YAAI,kBAAkB,IAAI,KAAK,OAAO,GAAG,CAAC;MAC5C;IACF;AACA,eAAW,OAAO,YAAY;AAC5B,UAAI,CAAC,OAAO,eAAe,GAAG,GAAG;AAC/B,YAAI,kBAAkB,IAAI,KAAK,MAAS;MAC1C;IACF;EACF;AACA,MAAI,UAAU,UAAU,OAAO;AAC7B,UAAM,YAAY,UAAU,SAAS,CAAA;AACrC,eAAW,OAAO,OAAO;AACvB,UAAI,CAAC,UAAU,MAAM,GAAG,GAAG,UAAU,GAAG,CAAC,GAAG;AAC1C,YAAI,iBAAiB,IAAI,KAAK,MAAM,GAAG,CAAC;MAC1C;IACF;AACA,eAAW,OAAO,WAAW;AAC3B,UAAI,CAAC,MAAM,eAAe,GAAG,GAAG;AAC9B,YAAI,iBAAiB,IAAI,KAAK,MAAS;MACzC;IACF;EACF;AAGA,MAAI,CAAC,UAAU,QAAQ,UAAU,MAAM,GAAG;AACxC,QAAI,UAAU,IAAI,MAAM;EAC1B;AACA,MAAI,YAAY,UAAU,WAAW,YAAY,UAAU,SAAS;AAClE,QAAI,kBAAkB,IAAI,SAAS,OAAO;EAC5C;AACF;AAEA,SAAS,YAAY,KAAkB,IAAY,OAAiB;AAElE,MAAI,IAAI,SAAS,IAAI,MAAM,YAAY,EAAE,YAAY,UAAU,IAAI,UAAU,MAAM,MAAM,IAAI;AAC3F,UAAM,UAAsB,EAAC,GAAG,OAAO,GAAE;AACzC,WAAO,QAAQ;AAGf,QAAI,SAAS,SAAS,MAAM,QAAQ;EACtC;AACF;AAIA,IAAI,eAAe;AAEb,SAAU,MAAM,OAAiB;AACrC,QAAM,UAAM,2BAAW,UAAU,EAAE,IAAI,OAAM;AAC7C,QAAM,eAAW,uBAAO,KAAK;AAC7B,QAAM,CAAC,EAAE,cAAc,QAAI,yBAAS,CAAC;AAErC,QAAM,SAAK,wBAAQ,MAAM,MAAM,MAAM,aAAa,kBAAkB,CAAA,CAAE;AAEtE,gCAAU,MAAK;AACb,QAAI,KAAK;AACP,YAAM,cAAc,MAAM,eAAe,aAAW,UAAU,CAAC;AAC/D,UAAI,GAAG,aAAa,WAAW;AAC/B,kBAAW;AAEX,aAAO,MAAK;AACV,YAAI,IAAI,aAAa,WAAW;AAEhC,YAAI,IAAI,SAAS,IAAI,MAAM,WAAW,IAAI,SAAS,EAAE,GAAG;AACtD,cAAI,YAAY,EAAE;QACpB;MACF;IACF;AACA,WAAO;EACT,GAAG,CAAC,GAAG,CAAC;AAGR,QAAM,QAAQ,OAAO,IAAI,SAAS,IAAI,SAAS,EAAE;AACjD,MAAI,OAAO;AACT,QAAI;AACF,kBAAY,KAAK,IAAI,OAAO,SAAS,OAAO;IAC9C,SAAS,OAAP;AACA,cAAQ,KAAK,KAAK;IACpB;EACF,OAAO;AACL,gBAAY,KAAK,IAAI,KAAK;EAC5B;AAGA,WAAS,UAAU;AAEnB,SAAO;AACT;;;AxB1HA,IAAA,eAAe;", "names": ["React", "import_react", "import_react", "module", "React", "import_react", "import_react_dom", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "React", "import_react", "import_react"]}