{"version": 3, "file": "events.d.ts", "sourceRoot": "", "sources": ["../../src/types/events.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAC,oBAAiB;AAE1E,OAAO,KAAK,EACV,GAAG,EACH,MAAM,EACN,KAAK,EACL,gBAAgB,EAChB,aAAa,EACb,aAAa,IAAI,cAAc,EAC/B,kBAAkB,EAClB,aAAa,EACb,kBAAkB,EAClB,iBAAiB,EACjB,kBAAkB,EAClB,aAAa,EACb,iBAAiB,IAAI,eAAe,EACrC,MAAM,aAAa,CAAC;AAErB,YAAY,EACV,aAAa,IAAI,QAAQ,EACzB,kBAAkB,EAClB,aAAa,EACb,kBAAkB,EAClB,iBAAiB,EACjB,kBAAkB,EAClB,aAAa,EACb,eAAe,EAChB,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IACzB,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC9C,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC5C,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC9C,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC9C,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC1C,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC7C,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC/C,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC/C,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC7C,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAChD,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC/C,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC7C,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAC9C,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;IAEhD,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAChD,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAC3C,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAC9C,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAChD,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAC3C,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAC9C,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAChD,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAC3C,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAC9C,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAClD,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAC7C,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAChD,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IACjD,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAC5C,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAE/C,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,aAAa,KAAK,IAAI,CAAC;IACrC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE,eAAe,KAAK,IAAI,CAAC;IAC9C,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,eAAe,KAAK,IAAI,CAAC;IAC5C,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE,eAAe,KAAK,IAAI,CAAC;IAE/C,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,aAAa,KAAK,IAAI,CAAC;IACtC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,aAAa,KAAK,IAAI,CAAC;IACpC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,aAAa,KAAK,IAAI,CAAC;IACtC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,aAAa,KAAK,IAAI,CAAC;IACpC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,KAAK,IAAI,CAAC;IAClC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,aAAa,KAAK,IAAI,CAAC;IACtC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,iBAAiB,GAAG,kBAAkB,KAAK,IAAI,CAAC;IAC7D,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC7C,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,KAAK,IAAI,CAAC;CAChD,CAAC;AAEF,UAAU,QAAQ,CAAC,OAAO,EAAE,cAAc,GAAG,SAAS;IACpD,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,OAAO,CAAC;IAChB,aAAa,EAAE,cAAc,CAAC;CAC/B;AAED,MAAM,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG;IACvC,IAAI,EAAE,OAAO,CAAC;IACd,KAAK,EAAE,KAAK,CAAC;CACd,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG,cAAc,GAAG;IAC3C,KAAK,EAAE,KAAK,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,iBAAiB,EAAE,CAAC;CAChC,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAC5B,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC,GAAG;IACjE,IAAI,EAAE,WAAW,GAAG,MAAM,GAAG,SAAS,GAAG,WAAW,GAAG,MAAM,GAAG,SAAS,CAAC;IAC1E,SAAS,EAAE,SAAS,CAAC;CACtB,CAAC,GACF,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC,GAAG;IACpD,IAAI,EACA,aAAa,GACb,QAAQ,GACR,WAAW,GACX,WAAW,GACX,MAAM,GACN,SAAS,GACT,YAAY,GACZ,OAAO,GACP,UAAU,CAAC;IACf,SAAS,EAAE,SAAS,CAAC;CACtB,CAAC,CAAC;AAEP,MAAM,MAAM,UAAU,GAAG;IACvB,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC;IACvB,MAAM,EAAE,KAAK,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,WAAW,CAAC,cAAc,GAAG,SAAS,IAAI,QAAQ,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAEvF,MAAM,MAAM,eAAe,GAAG,WAAW,GAAG;IAC1C,IAAI,EAAE,WAAW,GAAG,MAAM,GAAG,SAAS,CAAC;IACvC,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AAExD,MAAM,MAAM,oBAAoB,GAAG,cAAc,GAAG,mBAAmB,CAAC;AAExE,MAAM,MAAM,mBAAmB,GAAG,cAAc,GAAG,wBAAwB,CAAC"}