[{"message": "layers[0].paint.fill-opacity: -1 is less than the minimum value 0", "line": 16}, {"message": "layers[0].paint.fill-opacity-transition.delay: -1 is less than the minimum value 0", "line": 19}, {"message": "layers[0].paint.fill-opacity-transition: unknown property \"bad\"", "line": 20}, {"message": "layers[0].paint.bad-transition: unknown property \"bad-transition\"", "line": 22}, {"message": "layers[0].paint.fill-antialias-transition: unknown property \"fill-antialias-transition\"", "line": 25}, {"message": "layers[1].paint.fill-opacity: 1.5 is greater than the maximum value 1", "line": 36}, {"message": "layers[2].paint.fill-opacity: number expected, null found"}, {"message": "layers[3].paint.fill-opacity: unknown property \"foo\"", "line": 56}, {"message": "layers[3].paint.fill-color.stops[1][0]: unknown property \"magic\"", "line": 62}, {"message": "layers[3].paint.bad: unknown property \"bad\"", "line": 65}, {"message": "layers[4].paint.fill-opacity: \"fill-opacity\" does not support interpolation syntax\nUse an identity property function instead: `{ \"type\": \"identity\", \"property\": \"opacity\" }`.", "line": 74}]