name: publish-style-spec

on:
  workflow_dispatch

jobs:
  build_publish:
    name: Build, publish
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js 18 x64
        uses: actions/setup-node@v3.6.0
        with:
          node-version: 18
          architecture: x64
          registry-url: 'https://registry.npmjs.org'

      - name: Get version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@v1.3.1

      - name: Check tag does not exist yet
        run: if git rev-list v${{ steps.package-version.outputs.current-version }}; then echo "Tag already exists. Aborting the release process."; exit 1; fi

      - name: Tag commit and push
        uses: mathieudutour/github-tag-action@v6.1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          custom_tag: ${{ steps.package-version.outputs.current-version }}


      - name: Install NPM packages
        run: npm ci
      
      - name: Prepare release
        id: prepare_release
        run: |
          echo "version_tag=v${{ steps.package-version.outputs.current-version }}" >> $GITHUB_OUTPUT
          RELEASE_TYPE=$(node -e "console.log(require('semver').prerelease('${{ steps.package-version.outputs.current-version }}') ? 'prerelease' : 'regular')")
          echo "release_type=$RELEASE_TYPE" >> $GITHUB_OUTPUT

      - name: Build style spec
        run: |
          npm run generate-style-spec
          npm run generate-typings
          npm run build

      - name: Build Release Notes
        id: release_notes
        if: ${{ steps.prepare_release.outputs.release_type == 'regular' }}
        run: |
          RELEASE_NOTES_PATH="${PWD}/release_notes.txt"
          ./build/release-notes.js > ${RELEASE_NOTES_PATH}
          echo "release_notes=${RELEASE_NOTES_PATH}" >> $GITHUB_OUTPUT

      - name: Create GitHub Release (regular)
        id: create_regular_release
        if: ${{ steps.prepare_release.outputs.release_type == 'regular' }}
        uses: ncipollo/release-action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag: ${{ steps.prepare_release.outputs.version_tag }}
          name: ${{steps.prepare_release.outputs.version_tag }}
          bodyFile: ${{ steps.release_notes.outputs.release_notes }}
          draft: false
          prerelease: false

      - name: Publish NPM package (regular)
        if: ${{ steps.prepare_release.outputs.release_type == 'regular' }}
        run: |
          npm publish --access=public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_ORG_TOKEN }}

      - name: Publish NPM package (pre-release)
        if: ${{ steps.prepare_release.outputs.release_type == 'prerelease' }}
        run: |
          npm publish --access=public --tag next
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_ORG_TOKEN }}
