import React, { useState } from 'react';
import { BarChart3, TrendingUp, Calendar, Download, Filter, RefreshCw } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, AreaChart, Area, ScatterChart, Scatter } from 'recharts';

const Analytics: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('6months');
  const [selectedMetric, setSelectedMetric] = useState('ndvi');

  // Dados simulados para análises
  const ndviTrend = [
    { date: '2024-01', ndvi: 0.65, area: 2450, productivity: 85 },
    { date: '2024-02', ndvi: 0.72, area: 2450, productivity: 88 },
    { date: '2024-03', ndvi: 0.78, area: 2450, productivity: 92 },
    { date: '2024-04', ndvi: 0.82, area: 2450, productivity: 95 },
    { date: '2024-05', ndvi: 0.75, area: 2450, productivity: 90 },
    { date: '2024-06', ndvi: 0.68, area: 2450, productivity: 87 },
  ];

  const productivityData = [
    { farm: 'São João', soja: 3200, milho: 8500, algodao: 1800 },
    { farm: 'Esperança', soja: 2950, milho: 7800, algodao: 1650 },
    { farm: 'Progresso', soja: 3100, milho: 8200, algodao: 1750 },
  ];

  const correlationData = [
    { ndvi: 0.65, productivity: 85, rainfall: 120 },
    { ndvi: 0.72, productivity: 88, rainfall: 145 },
    { ndvi: 0.78, productivity: 92, rainfall: 165 },
    { ndvi: 0.82, productivity: 95, rainfall: 180 },
    { ndvi: 0.75, productivity: 90, rainfall: 155 },
    { ndvi: 0.68, productivity: 87, rainfall: 135 },
  ];

  const anomalyData = [
    { month: 'Jan', anomalies: 2, severity: 'low' },
    { month: 'Fev', anomalies: 1, severity: 'low' },
    { month: 'Mar', anomalies: 4, severity: 'medium' },
    { month: 'Abr', anomalies: 3, severity: 'medium' },
    { month: 'Mai', anomalies: 6, severity: 'high' },
    { month: 'Jun', anomalies: 2, severity: 'low' },
  ];

  const insights = [
    {
      title: 'Tendência de Crescimento',
      description: 'NDVI médio aumentou 15% nos últimos 3 meses',
      type: 'positive',
      impact: 'Alto'
    },
    {
      title: 'Correlação Chuva-Produtividade',
      description: 'Forte correlação (0.87) entre precipitação e NDVI',
      type: 'info',
      impact: 'Médio'
    },
    {
      title: 'Anomalias Detectadas',
      description: '6 anomalias identificadas em maio, requer atenção',
      type: 'warning',
      impact: 'Alto'
    },
    {
      title: 'Eficiência Hídrica',
      description: 'Melhoria de 12% na eficiência do uso da água',
      type: 'positive',
      impact: 'Médio'
    }
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Análise Avançada</h1>
              <p className="text-gray-600">Insights detalhados e correlações dos dados agrícolas</p>
            </div>
            <div className="flex items-center space-x-3">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              >
                <option value="3months">Últimos 3 meses</option>
                <option value="6months">Últimos 6 meses</option>
                <option value="1year">Último ano</option>
              </select>
              <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2">
                <Download className="w-4 h-4" />
                <span>Exportar</span>
              </button>
            </div>
          </div>
        </div>

        {/* Key Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {insights.map((insight, index) => (
            <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between mb-3">
                <div className={`w-3 h-3 rounded-full ${
                  insight.type === 'positive' ? 'bg-green-500' :
                  insight.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                }`} />
                <span className={`text-xs px-2 py-1 rounded-full ${
                  insight.impact === 'Alto' ? 'bg-red-100 text-red-700' :
                  insight.impact === 'Médio' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-green-100 text-green-700'
                }`}>
                  {insight.impact}
                </span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{insight.title}</h3>
              <p className="text-sm text-gray-600">{insight.description}</p>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* NDVI Trend Analysis */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Tendência NDVI vs Produtividade</h3>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={ndviTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis yAxisId="left" domain={[0, 1]} />
                <YAxis yAxisId="right" orientation="right" domain={[80, 100]} />
                <Tooltip />
                <Area yAxisId="left" type="monotone" dataKey="ndvi" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </div>

          {/* Productivity Comparison */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Produtividade por Fazenda</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={productivityData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="farm" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="soja" fill="#10b981" name="Soja (kg/ha)" />
                <Bar dataKey="milho" fill="#f59e0b" name="Milho (kg/ha)" />
                <Bar dataKey="algodao" fill="#3b82f6" name="Algodão (kg/ha)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Correlation Analysis */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Correlação NDVI x Produtividade</h3>
            <ResponsiveContainer width="100%" height={300}>
              <ScatterChart data={correlationData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="ndvi" name="NDVI" />
                <YAxis dataKey="productivity" name="Produtividade" />
                <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                <Scatter dataKey="productivity" fill="#10b981" />
              </ScatterChart>
            </ResponsiveContainer>
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                <strong>Coeficiente de Correlação:</strong> 0.87 (Forte correlação positiva)
              </p>
              <p className="text-sm text-gray-600 mt-1">
                Aumento no NDVI está fortemente associado ao aumento na produtividade
              </p>
            </div>
          </div>

          {/* Anomaly Detection */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Detecção de Anomalias</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={anomalyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="anomalies" fill="#ef4444" name="Anomalias Detectadas" />
              </BarChart>
            </ResponsiveContainer>
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Total de Anomalias:</span>
                <span className="font-medium text-gray-900">18</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Severidade Média:</span>
                <span className="font-medium text-yellow-600">Média</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Tendência:</span>
                <span className="font-medium text-green-600">Decrescente</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
