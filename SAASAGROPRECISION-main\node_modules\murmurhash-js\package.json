{"name": "murmurhash-js", "version": "1.0.0", "description": "Native JS murmur hash implementation", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/murmurhash-js.git"}, "keywords": ["murmur", "hash", "string", "murmur2", "murmur3", "fast"], "author": "<PERSON>", "license": "MIT", "readmeFilename": "README.md", "gitHead": "72aabce3f52cb8f16245692a69fd35951e165af0", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/murmurhash-js/issues"}}