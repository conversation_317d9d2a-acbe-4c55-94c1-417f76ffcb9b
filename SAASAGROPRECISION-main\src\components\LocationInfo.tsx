import React, { useState, useEffect } from 'react';
import { MapPin, Compass, Clock, Satellite, Signal } from 'lucide-react';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

interface LocationInfoProps {
  position: [number, number] | null;
  onLocationUpdate?: (location: LocationData) => void;
}

const LocationInfo: React.FC<LocationInfoProps> = ({ position, onLocationUpdate }) => {
  const [locationData, setLocationData] = useState<LocationData | null>(null);
  const [isWatching, setIsWatching] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);

  // Função para obter informações detalhadas de localização
  const getDetailedLocation = () => {
    if (!navigator.geolocation) return;

    const options = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0
    };

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const data: LocationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude || undefined,
          heading: position.coords.heading || undefined,
          speed: position.coords.speed || undefined,
          timestamp: position.timestamp
        };
        setLocationData(data);
        onLocationUpdate?.(data);
      },
      (error) => {
        console.error('Erro ao obter localização:', error);
      },
      options
    );
  };

  // Função para monitorar localização continuamente
  const startWatching = () => {
    if (!navigator.geolocation || isWatching) return;

    const options = {
      enableHighAccuracy: true,
      timeout: 5000,
      maximumAge: 1000
    };

    const id = navigator.geolocation.watchPosition(
      (position) => {
        const data: LocationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude || undefined,
          heading: position.coords.heading || undefined,
          speed: position.coords.speed || undefined,
          timestamp: position.timestamp
        };
        setLocationData(data);
        onLocationUpdate?.(data);
      },
      (error) => {
        console.error('Erro no monitoramento:', error);
      },
      options
    );

    setWatchId(id);
    setIsWatching(true);
  };

  // Função para parar o monitoramento
  const stopWatching = () => {
    if (watchId !== null) {
      navigator.geolocation.clearWatch(watchId);
      setWatchId(null);
      setIsWatching(false);
    }
  };

  // Limpar o watch quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [watchId]);

  // Função para formatar coordenadas
  const formatCoordinate = (coord: number, type: 'lat' | 'lng') => {
    const direction = type === 'lat' ? (coord >= 0 ? 'N' : 'S') : (coord >= 0 ? 'E' : 'W');
    return `${Math.abs(coord).toFixed(6)}° ${direction}`;
  };

  // Função para formatar precisão
  const getAccuracyLevel = (accuracy: number) => {
    if (accuracy <= 5) return { level: 'Excelente', color: 'text-green-600' };
    if (accuracy <= 10) return { level: 'Boa', color: 'text-blue-600' };
    if (accuracy <= 20) return { level: 'Moderada', color: 'text-yellow-600' };
    return { level: 'Baixa', color: 'text-red-600' };
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
          <Satellite className="w-5 h-5" />
          <span>Informações GPS</span>
        </h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={getDetailedLocation}
            className="px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
          >
            Atualizar
          </button>
          <button
            onClick={isWatching ? stopWatching : startWatching}
            className={`px-3 py-1 rounded-lg transition-colors text-sm ${
              isWatching 
                ? 'bg-red-600 text-white hover:bg-red-700' 
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isWatching ? 'Parar' : 'Monitorar'}
          </button>
        </div>
      </div>

      {locationData ? (
        <div className="space-y-4">
          {/* Coordenadas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-1">
                <MapPin className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Latitude</span>
              </div>
              <p className="text-sm text-gray-900 font-mono">
                {formatCoordinate(locationData.latitude, 'lat')}
              </p>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-1">
                <MapPin className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Longitude</span>
              </div>
              <p className="text-sm text-gray-900 font-mono">
                {formatCoordinate(locationData.longitude, 'lng')}
              </p>
            </div>
          </div>

          {/* Precisão */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Signal className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">Precisão</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-900">±{locationData.accuracy.toFixed(1)} metros</span>
              <span className={`text-xs px-2 py-1 rounded-full bg-gray-100 ${getAccuracyLevel(locationData.accuracy).color}`}>
                {getAccuracyLevel(locationData.accuracy).level}
              </span>
            </div>
          </div>

          {/* Informações adicionais */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {locationData.altitude && (
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Compass className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Altitude</span>
                </div>
                <p className="text-sm text-gray-900">{locationData.altitude.toFixed(1)} metros</p>
              </div>
            )}

            {locationData.speed !== undefined && locationData.speed > 0 && (
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Compass className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Velocidade</span>
                </div>
                <p className="text-sm text-gray-900">{(locationData.speed * 3.6).toFixed(1)} km/h</p>
              </div>
            )}
          </div>

          {/* Timestamp */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Clock className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">Última atualização</span>
            </div>
            <p className="text-sm text-gray-900">
              {new Date(locationData.timestamp).toLocaleString('pt-BR')}
            </p>
          </div>

          {/* Status do monitoramento */}
          {isWatching && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium text-green-800">Monitoramento ativo</span>
              </div>
              <p className="text-xs text-green-600 mt-1">
                A localização está sendo atualizada automaticamente
              </p>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-8">
          <Satellite className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-500 text-sm">
            Clique em "Atualizar" para obter informações de localização
          </p>
        </div>
      )}
    </div>
  );
};

export default LocationInfo;
