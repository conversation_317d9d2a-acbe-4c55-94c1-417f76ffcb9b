{"version": 3, "file": "source.js", "sourceRoot": "", "sources": ["../../../src/mapbox-legacy/components/source.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAC,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAC,MAAM,OAAO,CAAC;AACrF,OAAO,EAAC,UAAU,EAAC,iBAAc;AACjC,OAAO,MAAM,2BAAwB;AACrC,OAAO,EAAC,SAAS,EAAC,+BAA4B;AAmB9C,IAAI,aAAa,GAAG,CAAC,CAAC;AAEtB,SAAS,YAAY,CAAC,GAAgB,EAAE,EAAU,EAAE,KAAkB;IACpE,aAAa;IACb,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,EAAC,GAAG,KAAK,EAAC,CAAC;QAC3B,OAAO,OAAO,CAAC,EAAE,CAAC;QAClB,OAAO,OAAO,CAAC,QAAQ,CAAC;QACxB,aAAa;QACb,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAC3B,OAAO,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,+BAA+B;AAC/B,SAAS,YAAY,CAAC,MAA+B,EAAE,KAAkB,EAAE,SAAsB;IAC/F,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,EAAE,mBAAmB,CAAC,CAAC;IACvD,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;IAE7D,IAAI,UAAU,GAAG,EAAE,CAAC;IACpB,IAAI,eAAe,GAAG,CAAC,CAAC;IAExB,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;QACxB,IAAI,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACjF,UAAU,GAAG,GAAG,CAAC;YACjB,eAAe,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,OAAO;IACT,CAAC;IAED,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IAExB,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACtB,MAAsC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAW,CAAC,CAAC;IACrE,CAAC;SAAM,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3B,MAAkC,CAAC,WAAW,CAAC;YAC9C,GAAG,EAAE,KAAK,CAAC,GAAG;YACd,WAAW,EAAE,KAAK,CAAC,WAAW;SAC/B,CAAC,CAAC;IACL,CAAC;SAAM,IAAI,gBAAgB,IAAI,MAAM,IAAI,eAAe,KAAK,CAAC,IAAI,UAAU,KAAK,aAAa,EAAE,CAAC;QAC/F,MAAM,CAAC,cAAc,CAAE,KAA6C,CAAC,WAAW,CAAC,CAAC;IACpF,CAAC;SAAM,IAAI,QAAQ,IAAI,MAAM,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;QACtD,MAAM,CAAC,MAAM,CAAE,KAAmC,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;SAAM,IAAI,UAAU,IAAI,MAAM,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;QAC1D,MAAM,CAAC,QAAQ,CAAE,KAAmC,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;SAAM,CAAC;QACN,2BAA2B;QAC3B,OAAO,CAAC,IAAI,CAAC,mCAAmC,UAAU,EAAE,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AACD,8BAA8B;AAE9B,MAAM,UAAU,MAAM,CAAC,KAAkB;IACvC,MAAM,GAAG,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;IAChD,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEvC,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,cAAc,aAAa,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAE1E,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,GAAG,EAAE,CAAC;YACR,uBAAuB;YACvB,MAAM,WAAW,GAAG,GAAG,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtF,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACjC,WAAW,EAAE,CAAC;YAEd,OAAO,GAAG,EAAE;gBACV,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;gBAClC,aAAa;gBACb,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;oBACxD,sDAAsD;oBACtD,iDAAiD;oBACjD,gEAAgE;oBAChE,MAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;oBACzC,IAAI,SAAS,EAAE,CAAC;wBACd,KAAK,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;4BAC9B,6DAA6D;4BAC7D,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;gCACxB,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;4BAC5B,CAAC;wBACH,CAAC;oBACH,CAAC;oBACD,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAEV,aAAa;IACb,IAAI,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IACnD,IAAI,MAAM,EAAE,CAAC;QACX,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,YAAY,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IACxC,CAAC;IACD,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;IAEzB,OAAO,CACL,CAAC,MAAM;QACL,KAAK,CAAC,QAAQ,CAAC,GAAG,CAChB,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,EAAE,CACN,KAAK;YACL,YAAY,CAAC,KAAK,EAAE;gBAClB,MAAM,EAAE,EAAE;aACX,CAAC,CACL,CAAC;QACJ,IAAI,CACL,CAAC;AACJ,CAAC"}