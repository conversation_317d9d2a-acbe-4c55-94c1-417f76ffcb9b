{
  "root": true,
  "extends": [
    "mourner",
    "plugin:import/recommended",
    "plugin:import/typescript"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "sourceType": "module",
    "createDefaultProgram": true
  },
  "plugins": [
    "@typescript-eslint",
    "import",
    "jsdoc",
    "jest"
  ],
  "rules": {
    "flowtype/require-valid-file-annotation": [0],
    "no-dupe-class-members": "off",
    "@typescript-eslint/no-dupe-class-members": ["error"],
    "@typescript-eslint/no-unused-vars": [
      "warn",
      { "argsIgnorePattern": "^_" }
    ],
    "@typescript-eslint/member-delimiter-style": ["error"],
    "no-useless-constructor": "off",
    "@typescript-eslint/no-useless-constructor": ["error"],
    // Disable no-undef. It's covered by @typescript-eslint
    "no-undef": "off",
    // temporarily disabled due to https://github.com/babel/babel-eslint/issues/485
    "no-use-before-define": "off",

    // no-duplicate-imports doesn't play well with Flow
    // https://github.com/babel/eslint-plugin-babel/issues/59
    "no-duplicate-imports": "off",
    "import/no-duplicates": "error",

    // temporarily disabled for easier upgrading of dependencies
    "implicit-arrow-linebreak": "off",
    "arrow-parens": "off",
    "arrow-body-style": "off",
    "no-confusing-arrow": "off",
    "no-control-regex": "off",
    "no-invalid-this": "off",
    "no-buffer-constructor": "off",

    "array-bracket-spacing": "error",
    "consistent-return": "off",
    "global-require": "off",
    "import/no-commonjs": "error",
    // TSC throws an error on unresolved imports; eslint doesn't understand .js extension in import statement
    "import/no-unresolved": "off",
    "key-spacing": "error",
    "no-eq-null": "off",
    "no-lonely-if": "off",
    "no-new": "off",
    "no-restricted-properties": [2, {
        "object": "Object",
        "property": "assign"
    }],
    "no-unused-vars": "off",
    "no-warning-comments": "error",
    "object-curly-spacing": ["error", "never"],
    "prefer-arrow-callback": "error",
    "prefer-const": ["error", {"destructuring": "all"}],
    "prefer-template": "error",
    // @typescript-eslint/quotes requires standard quotes rule to be turned off
    "quotes": "off",
    "@typescript-eslint/quotes": ["error", "single"],
    "no-redeclare": "off",
    "@typescript-eslint/no-redeclare": ["error"],
    "space-before-function-paren": "off",
    "template-curly-spacing": "error",
    "no-useless-escape": "off",
    // @typescript-eslint/indent requires standard indent rule to be turned off
    "indent": "off",
    "@typescript-eslint/indent": ["error"],
    "no-multiple-empty-lines": [ "error", {
        "max": 1
    }],
    "import/no-anonymous-default-export": ["error", {"allowLiteral": true} ],
    "jsdoc/check-param-names": "warn",
    "jsdoc/require-param": "warn",
    "jsdoc/require-param-description": "warn",
    "jsdoc/require-param-name": "warn",
    "jsdoc/require-returns": "warn",
    "jsdoc/require-returns-description": "warn",
    "jsdoc/check-alignment": "error",
    "jsdoc/check-line-alignment": "error",

    // Jest https://www.npmjs.com/package/eslint-plugin-jest
    "jest/no-commented-out-tests": "error",
    "jest/no-disabled-tests": "warn",
    "jest/no-focused-tests": "error",
    "jest/prefer-to-contain": "warn",
    "jest/prefer-to-have-length": "warn",
    "jest/valid-expect": "error",
    "jest/prefer-to-be": "warn",
    "jest/no-alias-methods": "warn",
    "jest/no-interpolation-in-snapshots": "warn",
    "jest/no-large-snapshots": ["warn", { "maxSize": 50, "inlineMaxSize": 20 }],
    "jest/no-deprecated-functions": "warn"
  },
  "reportUnusedDisableDirectives": true,
  "settings": {
    "jsdoc":{
      "ignorePrivate": true
    }
  },
  "ignorePatterns": ["build/*.js", "*.json", "**/tsc/*", "**/dist/*"],
  "overrides": [
    {
      "files": ["test/**", "src/style-spec/**"],
      "rules": {
        "jsdoc/check-param-names": "off",
        "jsdoc/require-param": "off",
        "jsdoc/require-param-description": "off",
        "jsdoc/require-param-name": "off",
        "jsdoc/require-returns": "off",
        "jsdoc/require-returns-description": "off"
      }
    }
  ],
  "globals": {
    "performance": true
  },
  "env": {
    "es6": true,
    "browser": false
  }
}
