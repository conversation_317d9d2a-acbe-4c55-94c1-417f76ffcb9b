{"version": 3, "file": "marker.js", "sourceRoot": "", "sources": ["../../../src/mapbox-legacy/components/marker.ts"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAC,YAAY,EAAC,MAAM,WAAW,CAAC;AACvC,OAAO,EAAC,mBAAmB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAC,MAAM,OAAO,CAAC;AACpG,OAAO,EAAC,eAAe,EAAC,sCAAmC;AAK3D,OAAO,EAAC,UAAU,EAAC,iBAAc;AACjC,OAAO,EAAC,cAAc,EAAC,+BAA4B;AAmBnD,8CAA8C;AAC9C,MAAM,CAAC,MAAM,MAAM,GAAG,IAAI,CACxB,UAAU,CAAC,CAAC,KAAkB,EAAE,GAA8B,EAAE,EAAE;IAChE,MAAM,EAAC,GAAG,EAAE,MAAM,EAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;IAC7C,MAAM,OAAO,GAAG,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC;IAChC,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IAE9B,MAAM,MAAM,GAAmB,OAAO,CAAC,GAAG,EAAE;QAC1C,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE;YAC1C,IAAI,EAAE,EAAE,CAAC;gBACP,WAAW,GAAG,IAAI,CAAC;YACrB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,OAAO,GAAG;YACd,GAAG,KAAK;YACR,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;SAC5D,CAAC;QAEF,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACtC,EAAE,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEhD,EAAE,CAAC,UAAU,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAa,EAAE,EAAE;YAC1D,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,EAAE;gBACV,aAAa,EAAE,CAAC;aACjB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE;YACrB,MAAM,GAAG,GAAG,CAAoB,CAAC;YACjC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAChC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;YAChB,MAAM,GAAG,GAAG,CAAoB,CAAC;YACjC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAChC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE;YACnB,MAAM,GAAG,GAAG,CAAoB,CAAC;YACjC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAChC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,OAAO,EAAE,CAAC;IACZ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QAE3B,OAAO,GAAG,EAAE;YACV,MAAM,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,MAAM,EACN,KAAK,EACL,SAAS,GAAG,KAAK,EACjB,KAAK,GAAG,IAAI,EACZ,QAAQ,GAAG,CAAC,EACZ,iBAAiB,GAAG,MAAM,EAC1B,cAAc,GAAG,MAAM,EACxB,GAAG,KAAK,CAAC;IAEV,SAAS,CAAC,GAAG,EAAE;QACb,eAAe,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAE3C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;QAChF,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC1C,CAAC;IACD,IAAI,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;QAC1D,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IACD,IAAI,MAAM,CAAC,WAAW,EAAE,KAAK,SAAS,EAAE,CAAC;QACvC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC;IACD,IAAI,MAAM,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE,CAAC;QACtC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IACD,IAAI,MAAM,CAAC,oBAAoB,EAAE,KAAK,iBAAiB,EAAE,CAAC;QACxD,MAAM,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;IACjD,CAAC;IACD,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,cAAc,EAAE,CAAC;QAClD,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAC3C,CAAC;IACD,IAAI,MAAM,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE,CAAC;QAChC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;AAC3D,CAAC,CAAC,CACH,CAAC"}