{"version": 3, "file": "mapbox.d.ts", "sourceRoot": "", "sources": ["../../../src/mapbox-legacy/mapbox/mapbox.ts"], "names": [], "mappings": "AASA,OAAO,KAAK,EACV,SAAS,EAET,SAAS,EACT,cAAc,EACd,aAAa,EACb,gBAAgB,EAEjB,2BAAwB;AACzB,OAAO,KAAK,EACV,kBAAkB,EAClB,kBAAkB,EAClB,oBAAoB,EACpB,gBAAgB,EAChB,uBAAuB,EACxB,+BAA4B;AAC7B,OAAO,KAAK,EAAC,WAAW,EAAC,wBAAqB;AAC9C,OAAO,KAAK,EAAC,SAAS,EAAE,mBAAmB,EAAC,6BAA0B;AACtE,OAAO,KAAK,EACV,YAAY,EACZ,oBAAoB,EACpB,QAAQ,EAER,aAAa,EACd,2BAAwB;AAEzB,MAAM,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,GAC1C,YAAY,GAAG;IAEb,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAE3B,6DAA6D;IAC7D,gBAAgB,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG;QACtC,gHAAgH;QAChH,MAAM,CAAC,EAAE,gBAAgB,CAAC;QAC1B,6EAA6E;QAC7E,gBAAgB,CAAC,EAAE;YACjB,MAAM,CAAC,EAAE,SAAS,CAAC;YACnB,OAAO,CAAC,EAAE,MAAM,CAAC;YACjB,OAAO,CAAC,EAAE,MAAM,CAAC;YACjB,OAAO,CAAC,EAAE,MAAM,GAAG,cAAc,CAAC;SACnC,CAAC;KACH,CAAC;IAEF,yDAAyD;IACzD,EAAE,CAAC,EAAE,qBAAqB,CAAC;IAE3B,2DAA2D;IAC3D,SAAS,CAAC,EAAE,SAAS,GAAG;QACtB,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IAIF,mBAAmB;IACnB,QAAQ,CAAC,EAAE,MAAM,GAAG,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,CAAC,CAAC;IAC3E;;OAEG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB;;OAEG;IACH,UAAU,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,CAAC,MAAM,CAAC,CAAC;IACvE;mEAC+D;IAC/D,GAAG,CAAC,EAAE,gBAAgB,CAAC;IACvB,mCAAmC;IACnC,KAAK,CAAC,EAAE,kBAAkB,CAAC;IAC3B;mEAC+D;IAC/D,OAAO,CAAC,EAAE,oBAAoB,CAAC;IAE/B,gDAAgD;IAChD,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC/B,iBAAiB;IACjB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAwEJ;;GAEG;AACH,MAAM,CAAC,OAAO,OAAO,MAAM;IACzB,OAAO,CAAC,SAAS,CAAoC;IAErD,OAAO,CAAC,IAAI,CAA6B;IAEzC,KAAK,EAAE,WAAW,CAAC;IAQnB,OAAO,CAAC,gBAAgB,CAAY;IAGpC,OAAO,CAAC,eAAe,CAAkB;IACzC,OAAO,CAAC,SAAS,CAAkB;IACnC,OAAO,CAAC,gBAAgB,CAA6B;IACrD,OAAO,CAAC,eAAe,CAUrB;IAEF,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,CAAM;gBAG9B,QAAQ,EAAE;QAAC,KAAK,OAAO,EAAE,GAAG,GAAG,WAAW,CAAA;KAAC,EAC3C,KAAK,EAAE,WAAW,EAClB,SAAS,EAAE,cAAc;IAO3B,IAAI,GAAG,IAAI,WAAW,CAErB;IAED,IAAI,SAAS,IAAI,SAAS,CAEzB;IAED,QAAQ,CAAC,KAAK,EAAE,WAAW;IAsB3B,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,GAAG,MAAM;IA6CnE,WAAW,CAAC,SAAS,EAAE,cAAc;IAsFrC,OAAO;IASP,OAAO;IAOP,MAAM;IAgBN,sBAAsB,CAAC,GAAG,EAAE,GAAG;IAW/B,WAAW,CAAC,SAAS,EAAE,WAAW,GAAG,OAAO;IAmB5C,gBAAgB,CAAC,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,GAAG,OAAO;IA+CzE,eAAe,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,GAAG,OAAO;IAkBxE,YAAY,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,GAAG,OAAO;IAwBrE,sBAAsB,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,GAAG,OAAO;IA+B/E,eAAe,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,GAAG,OAAO;IAkBxE,QAAQ,MAAO,QAAQ,UAQrB;IAEF,OAAO,CAAC,sBAAsB;IAiB9B,YAAY,CAAC,CAAC,EAAE,aAAa;IA0B7B,eAAe,MAAO,aAAa,UAcjC;IAEF,cAAc,MAAO,oBAAoB,UAWvC;IAEF,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,GAAG,QAAQ,EAAE,UAAU,CAAC,EAAE,MAAM;IA2B5E,gBAAgB;IAyBhB,eAAe,EAAE,MAAM,IAAI,CAAC;CAC7B"}