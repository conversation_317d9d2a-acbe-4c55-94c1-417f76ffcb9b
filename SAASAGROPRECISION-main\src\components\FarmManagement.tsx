import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, MapPin, User, Crop, Save, X, Search } from 'lucide-react';

interface Farm {
  id?: number;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  total_area: number;
  crop_type: string;
  owner_name: string;
  owner_contact?: string;
  created_at?: string;
  fields_count?: number;
  images_count?: number;
}

interface Field {
  id?: number;
  farm_id: number;
  name: string;
  area: number;
  crop_type?: string;
  coordinates?: string;
}

const FarmManagement: React.FC = () => {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarm, setSelectedFarm] = useState<Farm | null>(null);
  const [fields, setFields] = useState<Field[]>([]);
  const [showFarmForm, setShowFarmForm] = useState(false);
  const [showFieldForm, setShowFieldForm] = useState(false);
  const [editingFarm, setEditingFarm] = useState<Farm | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  const [farmForm, setFarmForm] = useState<Farm>({
    name: '',
    address: '',
    latitude: 0,
    longitude: 0,
    total_area: 0,
    crop_type: '',
    owner_name: '',
    owner_contact: ''
  });

  const [fieldForm, setFieldForm] = useState<Field>({
    farm_id: 0,
    name: '',
    area: 0,
    crop_type: ''
  });

  const cropTypes = [
    'Soja', 'Milho', 'Algodão', 'Cana-de-açúcar', 'Café', 'Arroz', 
    'Feijão', 'Trigo', 'Sorgo', 'Girassol', 'Pastagem', 'Outros'
  ];

  // Simular dados iniciais (em produção, viria da API)
  useEffect(() => {
    const mockFarms: Farm[] = [
      {
        id: 1,
        name: 'Fazenda São João',
        address: 'Rodovia BR-163, Km 45, Sorriso - MT',
        latitude: -12.5489,
        longitude: -55.7183,
        total_area: 2450,
        crop_type: 'Soja',
        owner_name: 'João Silva',
        owner_contact: '(65) 99999-9999',
        created_at: '2024-01-15',
        fields_count: 8,
        images_count: 24
      },
      {
        id: 2,
        name: 'Fazenda Esperança',
        address: 'Estrada Municipal 234, Primavera do Leste - MT',
        latitude: -15.5561,
        longitude: -54.2961,
        total_area: 1890,
        crop_type: 'Milho',
        owner_name: 'Maria Santos',
        owner_contact: '(65) 88888-8888',
        created_at: '2024-02-20',
        fields_count: 6,
        images_count: 18
      }
    ];
    setFarms(mockFarms);
  }, []);

  const handleCreateFarm = () => {
    setEditingFarm(null);
    setFarmForm({
      name: '',
      address: '',
      latitude: 0,
      longitude: 0,
      total_area: 0,
      crop_type: '',
      owner_name: '',
      owner_contact: ''
    });
    setShowFarmForm(true);
  };

  const handleEditFarm = (farm: Farm) => {
    setEditingFarm(farm);
    setFarmForm(farm);
    setShowFarmForm(true);
  };

  const handleSaveFarm = () => {
    setLoading(true);
    
    // Simular salvamento (em produção, seria uma chamada à API)
    setTimeout(() => {
      if (editingFarm) {
        // Atualizar fazenda existente
        setFarms(farms.map(f => f.id === editingFarm.id ? { ...farmForm, id: editingFarm.id } : f));
      } else {
        // Criar nova fazenda
        const newFarm = { ...farmForm, id: Date.now(), created_at: new Date().toISOString().split('T')[0] };
        setFarms([...farms, newFarm]);
      }
      
      setShowFarmForm(false);
      setEditingFarm(null);
      setLoading(false);
    }, 1000);
  };

  const handleDeleteFarm = (farmId: number) => {
    if (window.confirm('Tem certeza que deseja excluir esta fazenda?')) {
      setFarms(farms.filter(f => f.id !== farmId));
      if (selectedFarm?.id === farmId) {
        setSelectedFarm(null);
        setFields([]);
      }
    }
  };

  const handleSelectFarm = (farm: Farm) => {
    setSelectedFarm(farm);
    // Simular carregamento de talhões (em produção, viria da API)
    const mockFields: Field[] = [
      { id: 1, farm_id: farm.id!, name: 'Talhão A1', area: 45.5, crop_type: 'Soja' },
      { id: 2, farm_id: farm.id!, name: 'Talhão A2', area: 38.2, crop_type: 'Soja' },
      { id: 3, farm_id: farm.id!, name: 'Talhão B1', area: 52.8, crop_type: 'Milho' }
    ];
    setFields(mockFields);
  };

  const handleCreateField = () => {
    if (!selectedFarm) return;
    
    setFieldForm({
      farm_id: selectedFarm.id!,
      name: '',
      area: 0,
      crop_type: ''
    });
    setShowFieldForm(true);
  };

  const handleSaveField = () => {
    const newField = { ...fieldForm, id: Date.now() };
    setFields([...fields, newField]);
    setShowFieldForm(false);
  };

  const filteredFarms = farms.filter(farm =>
    farm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    farm.owner_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    farm.crop_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestão de Fazendas</h1>
          <p className="text-gray-600">Cadastre e gerencie suas propriedades rurais e áreas de plantio</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Lista de Fazendas */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">Fazendas Cadastradas</h2>
                  <button
                    onClick={handleCreateFarm}
                    className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Nova Fazenda</span>
                  </button>
                </div>
                
                {/* Busca */}
                <div className="relative">
                  <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Buscar fazendas..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>
              </div>

              <div className="divide-y divide-gray-200">
                {filteredFarms.map((farm) => (
                  <div
                    key={farm.id}
                    className={`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${
                      selectedFarm?.id === farm.id ? 'bg-emerald-50 border-l-4 border-emerald-500' : ''
                    }`}
                    onClick={() => handleSelectFarm(farm)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">{farm.name}</h3>
                        <div className="space-y-2 text-sm text-gray-600">
                          <div className="flex items-center space-x-2">
                            <MapPin className="w-4 h-4" />
                            <span>{farm.address}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <User className="w-4 h-4" />
                            <span>{farm.owner_name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Crop className="w-4 h-4" />
                            <span>{farm.crop_type} • {farm.total_area} ha</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-4 mt-3">
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                            {farm.fields_count || 0} talhões
                          </span>
                          <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                            {farm.images_count || 0} imagens
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditFarm(farm);
                          }}
                          className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteFarm(farm.id!);
                          }}
                          className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Detalhes da Fazenda Selecionada */}
          <div className="space-y-6">
            {selectedFarm ? (
              <>
                {/* Informações da Fazenda */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalhes da Fazenda</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Nome</label>
                      <p className="text-gray-900">{selectedFarm.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Proprietário</label>
                      <p className="text-gray-900">{selectedFarm.owner_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Área Total</label>
                      <p className="text-gray-900">{selectedFarm.total_area} hectares</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Cultivo Principal</label>
                      <p className="text-gray-900">{selectedFarm.crop_type}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Coordenadas</label>
                      <p className="text-gray-900 text-sm">
                        {selectedFarm.latitude.toFixed(6)}, {selectedFarm.longitude.toFixed(6)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Talhões */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900">Talhões</h3>
                      <button
                        onClick={handleCreateField}
                        className="bg-blue-600 text-white px-3 py-1.5 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1 text-sm"
                      >
                        <Plus className="w-4 h-4" />
                        <span>Novo Talhão</span>
                      </button>
                    </div>
                  </div>

                  <div className="p-6">
                    {fields.length > 0 ? (
                      <div className="space-y-3">
                        {fields.map((field) => (
                          <div key={field.id} className="bg-gray-50 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-gray-900">{field.name}</h4>
                                <p className="text-sm text-gray-600">
                                  {field.area} ha • {field.crop_type || 'Não especificado'}
                                </p>
                              </div>
                              <button className="text-gray-400 hover:text-red-600">
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-center py-4">Nenhum talhão cadastrado</p>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <p className="text-gray-500 text-center">Selecione uma fazenda para ver os detalhes</p>
              </div>
            )}
          </div>
        </div>

        {/* Modal de Formulário de Fazenda */}
        {showFarmForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-gray-900">
                    {editingFarm ? 'Editar Fazenda' : 'Nova Fazenda'}
                  </h3>
                  <button
                    onClick={() => setShowFarmForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Nome da Fazenda</label>
                    <input
                      type="text"
                      value={farmForm.name}
                      onChange={(e) => setFarmForm({ ...farmForm, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="Ex: Fazenda São João"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Proprietário</label>
                    <input
                      type="text"
                      value={farmForm.owner_name}
                      onChange={(e) => setFarmForm({ ...farmForm, owner_name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="Nome do proprietário"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Endereço</label>
                  <input
                    type="text"
                    value={farmForm.address}
                    onChange={(e) => setFarmForm({ ...farmForm, address: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="Endereço completo da propriedade"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Latitude</label>
                    <input
                      type="number"
                      step="any"
                      value={farmForm.latitude}
                      onChange={(e) => setFarmForm({ ...farmForm, latitude: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="-12.5489"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Longitude</label>
                    <input
                      type="number"
                      step="any"
                      value={farmForm.longitude}
                      onChange={(e) => setFarmForm({ ...farmForm, longitude: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="-55.7183"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Área Total (ha)</label>
                    <input
                      type="number"
                      value={farmForm.total_area}
                      onChange={(e) => setFarmForm({ ...farmForm, total_area: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="2450"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tipo de Cultivo</label>
                    <select
                      value={farmForm.crop_type}
                      onChange={(e) => setFarmForm({ ...farmForm, crop_type: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Selecione o cultivo</option>
                      {cropTypes.map((crop) => (
                        <option key={crop} value={crop}>{crop}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Contato</label>
                    <input
                      type="text"
                      value={farmForm.owner_contact}
                      onChange={(e) => setFarmForm({ ...farmForm, owner_contact: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="(65) 99999-9999"
                    />
                  </div>
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button
                  onClick={() => setShowFarmForm(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSaveFarm}
                  disabled={loading}
                  className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
                >
                  <Save className="w-4 h-4" />
                  <span>{loading ? 'Salvando...' : 'Salvar'}</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FarmManagement;
