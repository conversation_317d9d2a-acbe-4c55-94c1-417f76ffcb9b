{"version": 3, "file": "layer.js", "sourceRoot": "", "sources": ["../../../src/mapbox-legacy/components/layer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAC,MAAM,OAAO,CAAC;AACvE,OAAO,EAAC,UAAU,EAAC,iBAAc;AACjC,OAAO,MAAM,2BAAwB;AACrC,OAAO,EAAC,SAAS,EAAC,+BAA4B;AAe9C,+CAA+C;AAC/C,SAAS,WAAW,CAAC,GAAgB,EAAE,EAAU,EAAE,KAAiB,EAAE,SAAqB;IACzF,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;IACtD,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;IAE5D,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC3D,OAAO;IACT,CAAC;IAED,uDAAuD;IACvD,MAAM,EAAC,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAC,GAAG,KAAK,CAAC;IAE5E,IAAI,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;QACpC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC9B,CAAC;IACD,IAAI,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;QAChC,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,IAAI,EAAE,CAAC;QAC1C,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC7C,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,GAAU,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QACD,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,GAAU,EAAE,SAAS,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;IACH,CAAC;IACD,IAAI,KAAK,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;QAC9B,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC;QACxC,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC3C,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,GAAU,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;QACD,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,GAAU,EAAE,SAAS,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;IACH,CAAC;IAED,uDAAuD;IACvD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QACzC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,OAAO,KAAK,SAAS,CAAC,OAAO,IAAI,OAAO,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;QACnE,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC;AAED,SAAS,WAAW,CAAC,GAAgB,EAAE,EAAU,EAAE,KAAiB;IAClE,aAAa;IACb,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAC5F,MAAM,OAAO,GAAe,EAAC,GAAG,KAAK,EAAE,EAAE,EAAC,CAAC;QAC3C,OAAO,OAAO,CAAC,QAAQ,CAAC;QAExB,aAAa;QACb,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;AACH,CAAC;AAED,8CAA8C;AAE9C,IAAI,YAAY,GAAG,CAAC,CAAC;AAErB,MAAM,UAAU,KAAK,CAAC,KAAiB;IACrC,MAAM,GAAG,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;IAChD,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEvC,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,aAAa,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAExE,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,GAAG,EAAE,CAAC;YACR,MAAM,WAAW,GAAG,GAAG,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YACjE,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACjC,WAAW,EAAE,CAAC;YAEd,OAAO,GAAG,EAAE;gBACV,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;gBAClC,aAAa;gBACb,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;oBACvD,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAEV,aAAa;IACb,MAAM,KAAK,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACnD,IAAI,KAAK,EAAE,CAAC;QACV,IAAI,CAAC;YACH,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,sBAAsB;QAC7C,CAAC;IACH,CAAC;SAAM,CAAC;QACN,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,4BAA4B;IAC5B,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;IAEzB,OAAO,IAAI,CAAC;AACd,CAAC"}