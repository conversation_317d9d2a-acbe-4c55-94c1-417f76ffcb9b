{"version": 3, "file": "popup.js", "sourceRoot": "", "sources": ["../../../src/mapbox-legacy/components/popup.ts"], "names": [], "mappings": "AAEA,OAAO,EAAC,YAAY,EAAC,MAAM,WAAW,CAAC;AACvC,OAAO,EAAC,mBAAmB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAC,MAAM,OAAO,CAAC;AACpG,OAAO,EAAC,eAAe,EAAC,sCAAmC;AAK3D,OAAO,EAAC,UAAU,EAAC,iBAAc;AACjC,OAAO,EAAC,SAAS,EAAC,+BAA4B;AAgB9C,mFAAmF;AACnF,SAAS,YAAY,CAAC,SAAiB;IACrC,OAAO,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACjE,CAAC;AAMD,8CAA8C;AAC9C,MAAM,CAAC,MAAM,KAAK,GAAG,IAAI,CACvB,UAAU,CAAC,CAAC,KAAiB,EAAE,GAA6B,EAAE,EAAE;IAC9D,MAAM,EAAC,GAAG,EAAE,MAAM,EAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;IAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,EAAE;QAC7B,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,MAAM,OAAO,GAAG,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC;IAChC,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IAE9B,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE;QACzB,MAAM,OAAO,GAAG,EAAC,GAAG,KAAK,EAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,EAAE,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChD,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;YAClB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAe,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,OAAO,EAAmB,CAAC;IAC7B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE;YAClB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAe,CAAC,CAAC;QACnD,CAAC,CAAC;QACF,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC3B,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QAEnD,OAAO,GAAG,EAAE;YACV,oDAAoD;YACpD,oEAAoE;YACpE,gEAAgE;YAChE,oFAAoF;YACpF,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC5B,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBACnB,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,SAAS,CAAC,GAAG,EAAE;QACb,eAAe,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IACnD,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAElB,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAE1C,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;QACnB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC1F,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YACnE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC;YACvF,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACpC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,EAAE,CAAC;YAChD,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAEpD,KAAK,MAAM,CAAC,IAAI,aAAa,EAAE,CAAC;gBAC9B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC1B,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;YACD,KAAK,MAAM,CAAC,IAAI,aAAa,EAAE,CAAC;gBAC9B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC1B,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;YACD,KAAK,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AACjD,CAAC,CAAC,CACH,CAAC"}