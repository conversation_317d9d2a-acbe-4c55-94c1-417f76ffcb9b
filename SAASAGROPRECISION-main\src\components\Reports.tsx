import React, { useState } from 'react';
import { FileText, Download, Calendar, Filter, Eye, Share2, Printer, Mail } from 'lucide-react';

const Reports: React.FC = () => {
  const [selectedReport, setSelectedReport] = useState<number | null>(null);
  const [filterPeriod, setFilterPeriod] = useState('month');
  const [filterType, setFilterType] = useState('all');

  const reports = [
    {
      id: 1,
      title: 'Relatório Mensal de NDVI',
      description: 'Análise completa do índice de vegetação para junho/2024',
      type: 'ndvi',
      date: '2024-06-30',
      status: 'completed',
      size: '2.4 MB',
      pages: 15,
      farm: 'Fazenda São João'
    },
    {
      id: 2,
      title: 'Análise de Produtividade',
      description: 'Comparativo de produtividade entre talhões',
      type: 'productivity',
      date: '2024-06-28',
      status: 'completed',
      size: '1.8 MB',
      pages: 12,
      farm: 'Todas as Fazendas'
    },
    {
      id: 3,
      title: 'Detecção de Anomalias',
      description: 'Relatório de anomalias detectadas por IA',
      type: 'anomalies',
      date: '2024-06-25',
      status: 'processing',
      size: '3.1 MB',
      pages: 18,
      farm: 'Fazenda Esperança'
    },
    {
      id: 4,
      title: 'Eficiência Hídrica',
      description: 'Análise do uso da água e irrigação',
      type: 'water',
      date: '2024-06-20',
      status: 'completed',
      size: '2.7 MB',
      pages: 14,
      farm: 'Fazenda São João'
    },
    {
      id: 5,
      title: 'Relatório Climático',
      description: 'Condições meteorológicas e impactos na cultura',
      type: 'weather',
      date: '2024-06-15',
      status: 'completed',
      size: '1.5 MB',
      pages: 8,
      farm: 'Todas as Fazendas'
    }
  ];

  const reportTemplates = [
    {
      id: 1,
      name: 'NDVI Mensal',
      description: 'Análise mensal do índice de vegetação',
      icon: '📊',
      frequency: 'Mensal'
    },
    {
      id: 2,
      name: 'Produtividade',
      description: 'Relatório de produtividade por talhão',
      icon: '🌾',
      frequency: 'Trimestral'
    },
    {
      id: 3,
      name: 'Anomalias',
      description: 'Detecção automática de anomalias',
      icon: '⚠️',
      frequency: 'Semanal'
    },
    {
      id: 4,
      name: 'Climático',
      description: 'Condições meteorológicas',
      icon: '🌤️',
      frequency: 'Quinzenal'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-700';
      case 'processing': return 'bg-yellow-100 text-yellow-700';
      case 'failed': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed': return 'Concluído';
      case 'processing': return 'Processando';
      case 'failed': return 'Erro';
      default: return 'Desconhecido';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'ndvi': return 'bg-green-100 text-green-700';
      case 'productivity': return 'bg-blue-100 text-blue-700';
      case 'anomalies': return 'bg-red-100 text-red-700';
      case 'water': return 'bg-cyan-100 text-cyan-700';
      case 'weather': return 'bg-purple-100 text-purple-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'ndvi': return 'NDVI';
      case 'productivity': return 'Produtividade';
      case 'anomalies': return 'Anomalias';
      case 'water': return 'Hídrico';
      case 'weather': return 'Climático';
      default: return 'Geral';
    }
  };

  const filteredReports = reports.filter(report => {
    if (filterType !== 'all' && report.type !== filterType) return false;
    
    const reportDate = new Date(report.date);
    const now = new Date();
    
    switch (filterPeriod) {
      case 'week':
        return (now.getTime() - reportDate.getTime()) <= 7 * 24 * 60 * 60 * 1000;
      case 'month':
        return (now.getTime() - reportDate.getTime()) <= 30 * 24 * 60 * 60 * 1000;
      case 'quarter':
        return (now.getTime() - reportDate.getTime()) <= 90 * 24 * 60 * 60 * 1000;
      default:
        return true;
    }
  });

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Relatórios</h1>
          <p className="text-gray-600">Geração e gerenciamento de relatórios analíticos</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Filters */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Relatórios Gerados</h2>
                <div className="flex items-center space-x-3">
                  <select
                    value={filterPeriod}
                    onChange={(e) => setFilterPeriod(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="week">Última semana</option>
                    <option value="month">Último mês</option>
                    <option value="quarter">Último trimestre</option>
                    <option value="all">Todos</option>
                  </select>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="all">Todos os tipos</option>
                    <option value="ndvi">NDVI</option>
                    <option value="productivity">Produtividade</option>
                    <option value="anomalies">Anomalias</option>
                    <option value="water">Hídrico</option>
                    <option value="weather">Climático</option>
                  </select>
                </div>
              </div>

              {/* Reports List */}
              <div className="space-y-3">
                {filteredReports.map((report) => (
                  <div
                    key={report.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-all ${
                      selectedReport === report.id
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedReport(selectedReport === report.id ? null : report.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-medium text-gray-900">{report.title}</h3>
                          <span className={`text-xs px-2 py-1 rounded-full ${getTypeColor(report.type)}`}>
                            {getTypeLabel(report.type)}
                          </span>
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(report.status)}`}>
                            {getStatusLabel(report.status)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{report.description}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span>📅 {new Date(report.date).toLocaleDateString('pt-BR')}</span>
                          <span>📄 {report.pages} páginas</span>
                          <span>💾 {report.size}</span>
                          <span>🏢 {report.farm}</span>
                        </div>
                      </div>
                      
                      {report.status === 'completed' && (
                        <div className="flex items-center space-x-2 ml-4">
                          <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                            <Download className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors">
                            <Share2 className="w-4 h-4" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Report Details */}
            {selectedReport && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Ações do Relatório</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <Eye className="w-6 h-6 text-blue-600 mb-2" />
                    <span className="text-sm font-medium text-gray-900">Visualizar</span>
                  </button>
                  <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <Download className="w-6 h-6 text-green-600 mb-2" />
                    <span className="text-sm font-medium text-gray-900">Download</span>
                  </button>
                  <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <Mail className="w-6 h-6 text-purple-600 mb-2" />
                    <span className="text-sm font-medium text-gray-900">Enviar</span>
                  </button>
                  <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <Printer className="w-6 h-6 text-gray-600 mb-2" />
                    <span className="text-sm font-medium text-gray-900">Imprimir</span>
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Gerar Novo Relatório</h3>
              <button className="w-full bg-emerald-600 text-white py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors flex items-center justify-center space-x-2 mb-4">
                <FileText className="w-4 h-4" />
                <span>Relatório Personalizado</span>
              </button>

              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Templates Rápidos</h4>
                {reportTemplates.map((template) => (
                  <button
                    key={template.id}
                    className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{template.icon}</span>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{template.name}</p>
                        <p className="text-xs text-gray-600">{template.frequency}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Statistics */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Estatísticas</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Total de Relatórios</span>
                  <span className="text-sm font-medium text-gray-900">{reports.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Este Mês</span>
                  <span className="text-sm font-medium text-gray-900">
                    {reports.filter(r => new Date(r.date).getMonth() === new Date().getMonth()).length}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Processando</span>
                  <span className="text-sm font-medium text-yellow-600">
                    {reports.filter(r => r.status === 'processing').length}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Tamanho Total</span>
                  <span className="text-sm font-medium text-gray-900">12.5 MB</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;
