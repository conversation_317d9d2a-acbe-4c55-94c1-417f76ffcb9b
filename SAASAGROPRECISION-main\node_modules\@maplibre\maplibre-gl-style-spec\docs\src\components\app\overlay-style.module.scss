.overlay {
  height: 100vh;
  width: 100vw;
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  background-color: white;

  ul {
    list-style-type: none;
    margin: 20px 0 0 20px;
    padding: 0;
  }
}

.overlay a {
  padding: 8px;
  text-decoration: none;
  font-size: 16px;
  color: #818181;
  display: block;
  width: 100%;
}

.overlay a:hover,
.overlay a:focus {
  color: black;
}

.overlay .closebtn {
  position: absolute;
  top: 20px;
  right: 45px;
  font-size: 60px;
}

@media screen and (max-height: 450px) {
  .overlay a {
    font-size: 20px;
  }
  .overlay .closebtn {
    font-size: 40px;
    top: 15px;
    right: 35px;
  }
}
