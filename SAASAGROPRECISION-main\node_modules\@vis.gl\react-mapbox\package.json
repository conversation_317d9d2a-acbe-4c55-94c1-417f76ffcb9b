{"name": "@vis.gl/react-mapbox", "description": "React components for Mapbox GL JS", "version": "8.0.4", "keywords": ["mapbox", "mapbox-gl", "react", "react mapbox"], "repository": {"type": "git", "url": "https://github.com/visgl/react-map-gl.git"}, "license": "MIT", "type": "module", "types": "dist/index.d.ts", "main": "dist/index.cjs", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["src", "dist", "README.md"], "devDependencies": {"mapbox-gl": "^3.9.0"}, "peerDependencies": {"mapbox-gl": ">=3.5.0", "react": ">=16.3.0", "react-dom": ">=16.3.0"}, "peerDependenciesMeta": {"mapbox-gl": {"optional": true}}, "gitHead": "c7112cf50d6985e8427d6b187d23a4d957791bb7"}