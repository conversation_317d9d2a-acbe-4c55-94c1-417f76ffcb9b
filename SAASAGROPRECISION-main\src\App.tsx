import React, { useState } from 'react';
import {
  Sprout,
  TrendingUp,
  Camera,
  BarChart3,
  Shield,
  Users,
  Check,
  Star,
  Menu,
  X,
  ArrowRight,
  Leaf,
  MapPin,
  Clock,
  Database,
  Zap
} from 'lucide-react';

// Importar componentes do dashboard
import Sidebar from './components/Sidebar';
import TestDashboard from './components/TestDashboard';
import Dashboard from './components/Dashboard';
import MapView from './components/MapView';
import Analytics from './components/Analytics';
import Reports from './components/Reports';
import FarmManagement from './components/FarmManagement';
import DroneImageAnalysis from './components/DroneImageAnalysis';

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState('mensal');
  const [currentView, setCurrentView] = useState('landing');
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Função para renderizar a view atual do dashboard
  const renderDashboardView = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard />;
      case 'mapa':
        return <MapView />;
      case 'fazendas':
        return <FarmManagement />;
      case 'analise-imagens':
        return <DroneImageAnalysis />;
      case 'analytics':
        return <Analytics />;
      case 'relatorios':
        return <Reports />;
      default:
        return <Dashboard />;
    }
  };

  // Função para fazer login (simulado)
  const handleLogin = () => {
    setIsLoggedIn(true);
    setCurrentView('dashboard');
  };

  // Função para logout
  const handleLogout = () => {
    setIsLoggedIn(false);
    setCurrentView('landing');
  };

  // Se estiver logado, mostrar o dashboard
  if (isLoggedIn) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar
          currentView={currentView}
          onViewChange={setCurrentView}
        />
        <main className="flex-1 overflow-auto">
          <div className="p-4 border-b border-gray-200 bg-white">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900">
                {currentView === 'dashboard' && 'Dashboard'}
                {currentView === 'mapa' && 'Mapa Interativo'}
                {currentView === 'fazendas' && 'Gestão de Fazendas'}
                {currentView === 'analise-imagens' && 'Análise de Imagens'}
                {currentView === 'analytics' && 'Análise Avançada'}
                {currentView === 'relatorios' && 'Relatórios'}
              </h2>
              <button
                onClick={handleLogout}
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                Sair
              </button>
            </div>
          </div>
          {renderDashboardView()}
        </main>
      </div>
    );
  }

  const features = [
    {
      icon: <Camera className="w-8 h-8 text-green-400" />,
      title: "Monitoramento por Drone",
      description: "Imagens aéreas de alta resolução com câmeras espectrais e infravermelhas para análise detalhada das culturas."
    },
    {
      icon: <Zap className="w-8 h-8 text-green-400" />,
      title: "Análise Espectral Avançada",
      description: "Processamento de imagens espectrais para identificar estresse hídrico, deficiências nutricionais e pragas."
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-green-400" />,
      title: "Análise de Dados Avançada",
      description: "Transforme dados das imagens de drone em insights acionáveis para maximizar a produtividade."
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-green-400" />,
      title: "Previsão de Safras",
      description: "Algoritmos de IA para prever rendimentos e otimizar o planejamento de colheita baseado em imagens aéreas."
    },
    {
      icon: <Shield className="w-8 h-8 text-green-400" />,
      title: "Detecção Precoce de Problemas",
      description: "Identificação precoce de doenças, pragas e deficiências através de análise espectral para ação preventiva."
    },
    {
      icon: <MapPin className="w-8 h-8 text-green-400" />,
      title: "Mapeamento de Precisão",
      description: "Mapas detalhados de variabilidade das culturas e aplicação localizada de insumos baseados em dados de drone."
    }
  ];

  const plans = {
    mensal: [
      {
        name: "Iniciante",
        price: "R$ 397",
        period: "/mês",
        description: "Perfeito para pequenos produtores",
        features: [
          "Até 300 hectares",
          "2 voos de drone por mês",
          "Análise básica espectral",
          "Relatórios mensais",
          "Suporte via email",
          "1 usuário"
        ],
        popular: false
      },
      {
        name: "Profissional",
        price: "R$ 897",
        period: "/mês",
        description: "Ideal para fazendas médias",
        features: [
          "Até 500 hectares",
          "4 voos de drone por mês",
          "Análise espectral completa",
          "Câmeras infravermelhas",
          "Relatórios semanais",
          "Suporte prioritário",
          "Até 5 usuários",
          
        ],
        popular: true
      },
      {
        name: "Enterprise",
        price: "R$ 1.897",
        period: "/mês",
        description: "Para grandes operações",
        features: [
          "Hectares ilimitados",
          "10 voos de drone por mês",
          "Todas as funcionalidades",
          "Análise personalizada",
          "Relatórios após operações do drone",
          "Suporte 24/7",
          "Usuários ilimitados",
          "Integração customizada",
          "Acesso para seu agrônomo"
        ],
        popular: false
      }
    ],
    anual: [
      {
        name: "Iniciante",
        price: "R$ 3.573",
        period: "/ano",
        description: "Perfeito para pequenos produtores",
        features: [
          "Até 300 hectares",
          "2 voos de drone por mês",
          "Análise básica espectral",
          "Relatórios mensais",
          "Suporte via email",
          "1 usuário",
          "2 meses grátis"
        ],
        popular: false,
        savings: "Economize R$ 1.191"
      },
      {
        name: "Profissional",
        price: "R$ 8.073",
        period: "/ano",
        description: "Ideal para fazendas médias",
        features: [
          "Até 500 hectares",
          "4 voos de drone por mês",
          "Análise espectral completa",
          "Câmeras infravermelhas",
          "Relatórios semanais",
          "Suporte prioritário",
          "Até 5 usuários",,
          "2 meses grátis"
        ],
        popular: true,
        savings: "Economize R$ 2.691"
      },
      {
        name: "Enterprise",
        price: "R$ 17.073",
        period: "/ano",
        description: "Para grandes operações",
        features: [
          "Hectares ilimitados",
          "10 voos de drone por mês",
          "Todas as funcionalidades",
          "Análise personalizada",
          "Relatórios após operações do drone",
          "Suporte 24/7",
          "Usuários ilimitados",
          "Integração customizada",
          "Acesso para seu agrônomo",
          "2 meses grátis"
        ],
        popular: false,
        savings: "Economize R$ 5.691"
      }
    ]
  };

  const testimonials = [
    {
      name: "Carlos Silva",
      role: "Fazenda São João - SP",
      content: "Os drones com câmeras espectrais revolucionaram nossa fazenda. Detectamos problemas que nem víamos no campo. Produtividade aumentou 35%.",
      rating: 5
    },
    {
      name: "Ana Santos",
      role: "Cooperativa Verde Vale - MG",
      content: "A análise infravermelha nos ajudou a identificar estresse hídrico antes dos sintomas aparecerem. Economia de água foi impressionante.",
      rating: 5
    },
    {
      name: "João Oliveira",
      role: "Fazenda Horizonte - MT",
      content: "ROI incrível! Os mapas de aplicação localizada baseados nas imagens de drone reduziram nossos custos com insumos em 30%.",
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="fixed top-0 w-full bg-slate-900/95 backdrop-blur-sm border-b border-slate-800 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <Sprout className="w-8 h-8 text-green-400" />
              <span className="text-xl font-bold">AgroPrecision</span>
            </div>
            
            <nav className="hidden md:flex space-x-8">
              <a href="#features" className="hover:text-green-400 transition-colors">Recursos</a>
              <a href="#pricing" className="hover:text-green-400 transition-colors">Preços</a>
              <a href="#testimonials" className="hover:text-green-400 transition-colors">Depoimentos</a>
              <a href="#contact" className="hover:text-green-400 transition-colors">Contato</a>
            </nav>

            <div className="hidden md:flex items-center space-x-4">
              <button
                onClick={handleLogin}
                className="text-slate-300 hover:text-white transition-colors"
              >
                Entrar
              </button>
              <button
                onClick={handleLogin}
                className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors"
              >
                Teste Grátis
              </button>
            </div>

            <button 
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-slate-800 border-t border-slate-700">
            <div className="px-4 py-2 space-y-2">
              <a href="#features" className="block py-2 hover:text-green-400">Recursos</a>
              <a href="#pricing" className="block py-2 hover:text-green-400">Preços</a>
              <a href="#testimonials" className="block py-2 hover:text-green-400">Depoimentos</a>
              <a href="#contact" className="block py-2 hover:text-green-400">Contato</a>
              <div className="pt-2 border-t border-slate-600">
                <button
                  onClick={handleLogin}
                  className="block w-full text-left py-2 text-slate-300"
                >
                  Entrar
                </button>
                <button
                  onClick={handleLogin}
                  className="w-full mt-2 bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg"
                >
                  Teste Grátis
                </button>
              </div>
            </div>
          </div>
        )}
      </header>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Monitore sua Fazenda com
              <span className="text-green-400 block">Drones Espectrais</span>
              de Alta Precisão
            </h1>
            <p className="text-xl md:text-2xl text-slate-300 mb-8 leading-relaxed">
              Câmeras espectrais e infravermelhas capturam dados invisíveis ao olho humano. 
              Detecte problemas antes que apareçam e aumente sua produtividade em até 40%.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                onClick={handleLogin}
                className="bg-green-600 hover:bg-green-700 px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105 flex items-center gap-2"
              >
                Comece Gratuitamente
                <ArrowRight className="w-5 h-5" />
              </button>
              <button
                onClick={handleLogin}
                className="border border-slate-600 hover:border-slate-500 px-8 py-4 rounded-lg text-lg font-semibold transition-colors"
              >
                Ver Demonstração
              </button>
            </div>
          </div>
          
          <div className="mt-16 relative">
            <div className="bg-gradient-to-r from-green-900/20 to-slate-800/20 rounded-2xl p-8 backdrop-blur-sm border border-slate-700">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                  <div className="text-3xl font-bold text-green-400 mb-2">40%</div>
                  <div className="text-slate-300">Aumento na Produtividade</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-green-400 mb-2">30%</div>
                  <div className="text-slate-300">Redução de Custos</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-green-400 mb-2">99%</div>
                  <div className="text-slate-300">Precisão nas Análises</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-800/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Tecnologia de Drone
              <span className="text-green-400 block">que Transforma sua Fazenda</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Nossa frota de drones equipados com câmeras espectrais e infravermelhas 
              revela informações invisíveis sobre suas culturas.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-slate-800 rounded-xl p-6 border border-slate-700 hover:border-green-400/50 transition-all hover:transform hover:-translate-y-1">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-slate-300 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Planos que Crescem
              <span className="text-green-400 block">com sua Fazenda</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
              Escolha o plano ideal para o tamanho da sua operação. 
              Todos incluem voos de drone, análise espectral e suporte completo.
            </p>
            
            <div className="flex justify-center mb-8">
              <div className="bg-slate-800 rounded-lg p-1 border border-slate-700">
                <button
                  onClick={() => setSelectedPlan('mensal')}
                  className={`px-6 py-2 rounded-md transition-all ${
                    selectedPlan === 'mensal' 
                      ? 'bg-green-600 text-white' 
                      : 'text-slate-300 hover:text-white'
                  }`}
                >
                  Mensal
                </button>
                <button
                  onClick={() => setSelectedPlan('anual')}
                  className={`px-6 py-2 rounded-md transition-all ${
                    selectedPlan === 'anual' 
                      ? 'bg-green-600 text-white' 
                      : 'text-slate-300 hover:text-white'
                  }`}
                >
                  Anual
                  <span className="ml-2 text-xs bg-green-400 text-slate-900 px-2 py-1 rounded-full">
                    -25%
                  </span>
                </button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans[selectedPlan as keyof typeof plans].map((plan, index) => (
              <div key={index} className={`relative bg-slate-800 rounded-xl p-8 border transition-all hover:transform hover:-translate-y-1 ${
                plan.popular 
                  ? 'border-green-400 ring-2 ring-green-400/20' 
                  : 'border-slate-700 hover:border-green-400/50'
              }`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-green-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Mais Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                  <p className="text-slate-300 mb-4">{plan.description}</p>
                  <div className="mb-2">
                    <span className="text-4xl font-bold">{plan.price}</span>
                    <span className="text-slate-300">{plan.period}</span>
                  </div>
                  {plan.savings && (
                    <div className="text-green-400 text-sm font-semibold">
                      {plan.savings}
                    </div>
                  )}
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <Check className="w-5 h-5 text-green-400 mr-3 flex-shrink-0" />
                      <span className="text-slate-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button className={`w-full py-3 rounded-lg font-semibold transition-all ${
                  plan.popular
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'border border-slate-600 hover:border-green-400 hover:text-green-400'
                }`}>
                  Começar Agora
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-800/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Produtores que Confiam
              <span className="text-green-400 block">em Nossa Tecnologia</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Veja como nossos drones espectrais estão transformando fazendas em todo o Brasil com resultados reais.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-slate-800 rounded-xl p-6 border border-slate-700">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-slate-300 mb-6 italic">"{testimonial.content}"</p>
                <div className="border-t border-slate-700 pt-4">
                  <div className="font-semibold">{testimonial.name}</div>
                  <div className="text-slate-400 text-sm">{testimonial.role}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-r from-green-900/30 to-slate-800/30 rounded-2xl p-12 border border-slate-700">
            <Camera className="w-16 h-16 text-green-400 mx-auto mb-6" />
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Pronto para Ver sua Fazenda
              <span className="text-green-400 block">com Outros Olhos?</span>
            </h2>
            <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
              Junte-se a milhares de produtores que já descobriram o poder das câmeras espectrais. 
              Veja o que seus olhos não conseguem detectar.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={handleLogin}
                className="bg-green-600 hover:bg-green-700 px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105 flex items-center justify-center gap-2"
              >
                Teste Gratuito por 30 Dias
                <ArrowRight className="w-5 h-5" />
              </button>
              <button className="border border-slate-600 hover:border-slate-500 px-8 py-4 rounded-lg text-lg font-semibold transition-colors flex items-center justify-center gap-2">
                <Clock className="w-5 h-5" />
                Agendar Demonstração
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="bg-slate-900 border-t border-slate-800 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <Sprout className="w-8 h-8 text-green-400" />
                <span className="text-xl font-bold">AgroPrecision</span>
              </div>
              <p className="text-slate-300 mb-6 max-w-md">
                Transformando a agricultura brasileira com drones espectrais de alta tecnologia. 
                Sua fazenda mais produtiva e sustentável através de análises invisíveis.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center hover:bg-green-600 cursor-pointer transition-colors">
                  <Users className="w-5 h-5" />
                </div>
                <div className="w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center hover:bg-green-600 cursor-pointer transition-colors">
                  <Users className="w-5 h-5" />
                </div>
                <div className="w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center hover:bg-green-600 cursor-pointer transition-colors">
                  <Users className="w-5 h-5" />
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4 text-green-400">Produto</h4>
              <ul className="space-y-2 text-slate-300">
                <li><a href="#" className="hover:text-white transition-colors">Recursos</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Preços</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrações</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4 text-green-400">Suporte</h4>
              <ul className="space-y-2 text-slate-300">
                <li><a href="#" className="hover:text-white transition-colors">Documentação</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Centro de Ajuda</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contato</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Status</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-slate-800 mt-8 pt-8 text-center text-slate-400">
            <p>&copy; 2025 AgroPrecision. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;