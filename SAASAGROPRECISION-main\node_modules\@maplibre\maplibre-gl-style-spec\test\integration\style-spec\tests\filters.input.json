{"version": 8, "sources": {"source": {"type": "vector", "url": "https://demotiles.maplibre.org/tiles/tiles.json"}}, "layers": [{"id": "not-array", "type": "line", "source": "source", "source-layer": "source-layer", "filter": {}}, {"id": "zero-elements", "type": "line", "source": "source", "source-layer": "source-layer", "filter": []}, {"id": "invalid-operator", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["=", "key", "value"]}, {"id": "missing-key", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["=="]}, {"id": "missing-value", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["==", "key"]}, {"id": "invalid-key", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["==", 1, "value"]}, {"id": "invalid-value", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["==", "key", []]}, {"id": "invalid-type", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["==", "$type", "value"]}, {"id": "invalid-type-operator", "type": "line", "source": "source", "source-layer": "source-layer", "filter": [">", "$type", "value"]}, {"id": "invalid-nested-filter", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["any", []]}, {"id": "valid-has-filter", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["has", "mapbox"]}, {"id": "valid-!has-filter", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["!has", "mapbox"]}, {"id": "invalid-has-filter-length", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["has", "mapbox", "bad"]}, {"id": "invalid-has-filter-type", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["has", {"mapbox": true}]}, {"id": "mixing old filters and expressions", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["all", [">=", "Constructi", 1930], [">=", ["zoom"], 10]]}, {"id": "filter expressions with feature-state", "type": "line", "source": "source", "source-layer": "source-layer", "filter": ["<=", ["feature-state", "height"], 10]}]}