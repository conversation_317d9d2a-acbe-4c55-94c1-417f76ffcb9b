import type { PaddingOptions } from 'mapbox-gl';
export type { Point, PointLike, LngLat, LngLatLike, LngLatBounds, LngLatBoundsLike, PaddingOptions, MapboxGeoJSONFeature as MapGeoJSONFeature } from 'mapbox-gl';
/** Describes the camera's state */
export type ViewState = {
    /** Longitude at map center */
    longitude: number;
    /** Latitude at map center */
    latitude: number;
    /** Map zoom level */
    zoom: number;
    /** Map rotation bearing in degrees counter-clockwise from north */
    bearing: number;
    /** Map angle in degrees at which the camera is looking at the ground */
    pitch: number;
    /** Dimensions in pixels applied on each side of the viewport for shifting the vanishing point. */
    padding: PaddingOptions;
};
export interface ImmutableLike<T> {
    toJS: () => T;
}
//# sourceMappingURL=common.d.ts.map