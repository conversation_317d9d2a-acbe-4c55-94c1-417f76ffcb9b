// Generated by dts-bundle-generator v8.0.1

declare const _default: any;
/**
 * Given an array of layers, some of which may contain `ref` properties
 * whose value is the `id` of another property, return a new array where
 * such layers have been augmented with the 'type', 'source', etc. properties
 * from the parent layer, and the `ref` property has been removed.
 *
 * The input is not modified. The output may contain references to portions
 * of the input.
 *
 * @private
 * @param {Array<Layer>} layers
 * @returns {Array<Layer>}
 */
export declare function derefLayers(layers: any): any;
export declare const operations: {
	setStyle: string;
	addLayer: string;
	removeLayer: string;
	setPaintProperty: string;
	setLayoutProperty: string;
	setFilter: string;
	addSource: string;
	removeSource: string;
	setGeoJSONSourceData: string;
	setLayerZoomRange: string;
	setLayerProperty: string;
	setCenter: string;
	setZoom: string;
	setBearing: string;
	setPitch: string;
	setSprite: string;
	setGlyphs: string;
	setTransition: string;
	setLight: string;
};
declare function diffStyles(before: any, after: any): any[];
export declare class ValidationError {
	message: string;
	identifier: string;
	line: number;
	constructor(key: string, value: any & {
		__line__: number;
	}, message: string, identifier?: string | null);
}
export declare class ParsingError {
	message: string;
	error: Error;
	line: number;
	constructor(error: Error);
}
declare class ExpressionParsingError extends Error {
	key: string;
	message: string;
	constructor(key: string, message: string);
}
/**
 * @param r Red component 0..1
 * @param g Green component 0..1
 * @param b Blue component 0..1
 * @param alpha Alpha component 0..1
 */
export type RGBColor = [
	r: number,
	g: number,
	b: number,
	alpha: number
];
/**
 * @param h Hue as degrees 0..360
 * @param c Chroma 0..~230
 * @param l Lightness as percentage 0..100
 * @param alpha Alpha component 0..1
 */
export type HCLColor = [
	h: number,
	c: number,
	l: number,
	alpha: number
];
/**
 * @param l Lightness as percentage 0..100
 * @param a A axis value -125..125
 * @param b B axis value -125..125
 * @param alpha Alpha component 0..1
 */
export type LABColor = [
	l: number,
	a: number,
	b: number,
	alpha: number
];
/**
 * Color representation used by WebGL.
 * Defined in sRGB color space and pre-blended with alpha.
 * @private
 */
export declare class Color {
	readonly r: number;
	readonly g: number;
	readonly b: number;
	readonly a: number;
	/**
	 * @param r Red component premultiplied by `alpha` 0..1
	 * @param g Green component premultiplied by `alpha` 0..1
	 * @param b Blue component premultiplied by `alpha` 0..1
	 * @param [alpha=1] Alpha component 0..1
	 * @param [premultiplied=true] Whether the `r`, `g` and `b` values have already
	 * been multiplied by alpha. If `true` nothing happens if `false` then they will
	 * be multiplied automatically.
	 */
	constructor(r: number, g: number, b: number, alpha?: number, premultiplied?: boolean);
	static black: Color;
	static white: Color;
	static transparent: Color;
	static red: Color;
	/**
	 * Parses CSS color strings and converts colors to sRGB color space if needed.
	 * Officially supported color formats:
	 * - keyword, e.g. 'aquamarine' or 'steelblue'
	 * - hex (with 3, 4, 6 or 8 digits), e.g. '#f0f' or '#e9bebea9'
	 * - rgb and rgba, e.g. 'rgb(0,240,120)' or 'rgba(0%,94%,47%,0.1)' or 'rgb(0 240 120 / .3)'
	 * - hsl and hsla, e.g. 'hsl(0,0%,83%)' or 'hsla(0,0%,83%,.5)' or 'hsl(0 0% 83% / 20%)'
	 *
	 * @param input CSS color string to parse.
	 * @returns A `Color` instance, or `undefined` if the input is not a valid color string.
	 */
	static parse(input: Color | string | undefined | null): Color | undefined;
	/**
	 * Used in color interpolation and by 'to-rgba' expression.
	 *
	 * @returns Gien color, with reversed alpha blending, in sRGB color space.
	 */
	get rgb(): RGBColor;
	/**
	 * Used in color interpolation.
	 *
	 * @returns Gien color, with reversed alpha blending, in HCL color space.
	 */
	get hcl(): HCLColor;
	/**
	 * Used in color interpolation.
	 *
	 * @returns Gien color, with reversed alpha blending, in LAB color space.
	 */
	get lab(): LABColor;
	/**
	 * Lazy getter pattern. When getter is called for the first time lazy value
	 * is calculated and then overwrites getter function in given object instance.
	 *
	 * @example:
	 * const redColor = Color.parse('red');
	 * let x = redColor.hcl; // this will invoke `get hcl()`, which will calculate
	 * // the value of red in HCL space and invoke this `overwriteGetter` function
	 * // which in turn will set a field with a key 'hcl' in the `redColor` object.
	 * // In other words it will override `get hcl()` from its `Color` prototype
	 * // with its own property: hcl = [calculated red value in hcl].
	 * let y = redColor.hcl; // next call will no longer invoke getter but simply
	 * // return the previously calculated value
	 * x === y; // true - `x` is exactly the same object as `y`
	 *
	 * @param getterKey Getter key
	 * @param lazyValue Lazily calculated value to be memoized by current instance
	 * @private
	 */
	private overwriteGetter;
	/**
	 * Used by 'to-string' expression.
	 *
	 * @returns Serialized color in format `rgba(r,g,b,a)`
	 * where r,g,b are numbers within 0..255 and alpha is number within 1..0
	 *
	 * @example
	 * var purple = new Color.parse('purple');
	 * purple.toString; // = "rgba(128,0,128,1)"
	 * var translucentGreen = new Color.parse('rgba(26, 207, 26, .73)');
	 * translucentGreen.toString(); // = "rgba(26,207,26,0.73)"
	 */
	toString(): string;
}
declare class Intl$Collator {
	constructor(locales?: string | string[], options?: CollatorOptions);
	compare(a: string, b: string): number;
	resolvedOptions(): any;
}
export type CollatorOptions = {
	localeMatcher?: "lookup" | "best fit";
	usage?: "sort" | "search";
	sensitivity?: "base" | "accent" | "case" | "variant";
	ignorePunctuation?: boolean;
	numeric?: boolean;
	caseFirst?: "upper" | "lower" | "false";
};
declare class Collator {
	locale: string | null;
	sensitivity: "base" | "accent" | "case" | "variant";
	collator: Intl$Collator;
	constructor(caseSensitive: boolean, diacriticSensitive: boolean, locale: string | null);
	compare(lhs: string, rhs: string): number;
	resolvedLocale(): string;
}
export type ResolvedImageOptions = {
	name: string;
	available: boolean;
};
export declare class ResolvedImage {
	name: string;
	available: boolean;
	constructor(options: ResolvedImageOptions);
	toString(): string;
	static fromString(name: string): ResolvedImage | null;
}
export declare class FormattedSection {
	text: string;
	image: ResolvedImage | null;
	scale: number | null;
	fontStack: string | null;
	textColor: Color | null;
	constructor(text: string, image: ResolvedImage | null, scale: number | null, fontStack: string | null, textColor: Color | null);
}
export declare class Formatted {
	sections: Array<FormattedSection>;
	constructor(sections: Array<FormattedSection>);
	static fromString(unformatted: string): Formatted;
	isEmpty(): boolean;
	static factory(text: Formatted | string): Formatted;
	toString(): string;
}
/**
 * A set of four numbers representing padding around a box. Create instances from
 * bare arrays or numeric values using the static method `Padding.parse`.
 * @private
 */
export declare class Padding {
	/** Padding values are in CSS order: top, right, bottom, left */
	values: [
		number,
		number,
		number,
		number
	];
	constructor(values: [
		number,
		number,
		number,
		number
	]);
	/**
	 * Numeric padding values
	 * @param input A padding value
	 * @returns A `Padding` instance, or `undefined` if the input is not a valid padding value.
	 */
	static parse(input?: number | number[] | Padding | null): Padding | undefined;
	toString(): string;
}
export type ColorSpecification = string;
export type PaddingSpecification = number | number[];
export type VariableAnchorOffsetCollectionSpecification = Array<string | [
	number,
	number
]>;
export type SpriteSpecification = string | {
	id: string;
	url: string;
}[];
export type FormattedSpecification = string;
export type ResolvedImageSpecification = string;
export type PromoteIdSpecification = {
	[_: string]: string;
} | string;
export type ExpressionInputType = string | number | boolean;
export type CollatorExpressionSpecification = [
	"collator",
	{
		"case-sensitive"?: boolean | ExpressionSpecification;
		"diacritic-sensitive"?: boolean | ExpressionSpecification;
		locale?: string | ExpressionSpecification;
	}
];
export type InterpolationSpecification = [
	"linear"
] | [
	"exponential",
	number | ExpressionSpecification
] | [
	"cubic-bezier",
	number | ExpressionSpecification,
	number | ExpressionSpecification,
	number | ExpressionSpecification,
	number | ExpressionSpecification
];
export type ExpressionSpecification = [
	"array",
	unknown | ExpressionSpecification
] | [
	"array",
	ExpressionInputType | ExpressionSpecification,
	unknown | ExpressionSpecification
] | [
	"array",
	ExpressionInputType | ExpressionSpecification,
	number | ExpressionSpecification,
	unknown | ExpressionSpecification
] | [
	"boolean",
	...(unknown | ExpressionSpecification)[],
	unknown | ExpressionSpecification
] | CollatorExpressionSpecification | [
	"format",
	...(string | [
		"image",
		ExpressionSpecification
	] | ExpressionSpecification | {
		"font-scale"?: number | ExpressionSpecification;
		"text-font"?: string[] | ExpressionSpecification;
		"text-color"?: ColorSpecification | ExpressionSpecification;
	})[]
] | [
	"image",
	unknown | ExpressionSpecification
] | [
	"literal",
	unknown
] | [
	"number",
	unknown | ExpressionSpecification,
	...(unknown | ExpressionSpecification)[]
] | [
	"number-format",
	number | ExpressionSpecification,
	{
		"locale"?: string | ExpressionSpecification;
		"currency"?: string | ExpressionSpecification;
		"min-fraction-digits"?: number | ExpressionSpecification;
		"max-fraction-digits"?: number | ExpressionSpecification;
	}
] | [
	"object",
	unknown | ExpressionSpecification,
	...(unknown | ExpressionSpecification)[]
] | [
	"string",
	unknown | ExpressionSpecification,
	...(unknown | ExpressionSpecification)[]
] | [
	"to-boolean",
	unknown | ExpressionSpecification
] | [
	"to-color",
	unknown | ExpressionSpecification,
	...(unknown | ExpressionSpecification)[]
] | [
	"to-number",
	unknown | ExpressionSpecification,
	...(unknown | ExpressionSpecification)[]
] | [
	"to-string",
	unknown | ExpressionSpecification
] | [
	"accumulated"
] | [
	"feature-state",
	string
] | [
	"geometry-type"
] | [
	"id"
] | [
	"line-progress"
] | [
	"properties"
] | [
	"at",
	number | ExpressionSpecification,
	ExpressionSpecification
] | [
	"get",
	string | ExpressionSpecification,
	(Record<string, unknown> | ExpressionSpecification)?
] | [
	"has",
	string | ExpressionSpecification,
	(Record<string, unknown> | ExpressionSpecification)?
] | [
	"in",
	ExpressionInputType | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification
] | [
	"index-of",
	ExpressionInputType | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification
] | [
	"length",
	string | ExpressionSpecification
] | [
	"slice",
	string | ExpressionSpecification,
	number | ExpressionSpecification,
	(number | ExpressionSpecification)?
] | [
	"!",
	boolean | ExpressionSpecification
] | [
	"!=",
	ExpressionInputType | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification,
	CollatorExpressionSpecification?
] | [
	"<",
	ExpressionInputType | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification,
	CollatorExpressionSpecification?
] | [
	"<=",
	ExpressionInputType | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification,
	CollatorExpressionSpecification?
] | [
	"==",
	ExpressionInputType | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification,
	CollatorExpressionSpecification?
] | [
	">",
	ExpressionInputType | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification,
	CollatorExpressionSpecification?
] | [
	">=",
	ExpressionInputType | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification,
	CollatorExpressionSpecification?
] | [
	"all",
	...(boolean | ExpressionSpecification)[]
] | [
	"any",
	...(boolean | ExpressionSpecification)[]
] | [
	"case",
	boolean | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification,
	...(boolean | ExpressionInputType | ExpressionSpecification)[],
	ExpressionInputType | ExpressionSpecification
] | [
	"coalesce",
	...(ExpressionInputType | ExpressionSpecification)[]
] | [
	"match",
	ExpressionInputType | ExpressionSpecification,
	ExpressionInputType | ExpressionInputType[],
	ExpressionInputType | ExpressionSpecification,
	...(ExpressionInputType | ExpressionInputType[] | ExpressionSpecification)[],
	// repeated as above
	ExpressionInputType | ExpressionSpecification
] | [
	"within",
	unknown | ExpressionSpecification
] | [
	"interpolate",
	InterpolationSpecification,
	number | ExpressionSpecification,
	...(number | number[] | ColorSpecification | ExpressionSpecification)[]
] | [
	"interpolate-hcl",
	InterpolationSpecification,
	number | ExpressionSpecification,
	...(number | ColorSpecification)[]
] | [
	"interpolate-lab",
	InterpolationSpecification,
	number | ExpressionSpecification,
	...(number | ColorSpecification)[]
] | [
	"step",
	number | ExpressionSpecification,
	ExpressionInputType | ExpressionSpecification,
	...(number | ExpressionInputType | ExpressionSpecification)[]
] | [
	"let",
	string,
	ExpressionInputType | ExpressionSpecification,
	...(string | ExpressionInputType | ExpressionSpecification)[]
] | [
	"var",
	string
] | [
	"concat",
	...(ExpressionInputType | ExpressionSpecification)[]
] | [
	"downcase",
	string | ExpressionSpecification
] | [
	"is-supported-script",
	string | ExpressionSpecification
] | [
	"resolved-locale",
	CollatorExpressionSpecification
] | [
	"upcase",
	string | ExpressionSpecification
] | [
	"rgb",
	number | ExpressionSpecification,
	number | ExpressionSpecification,
	number | ExpressionSpecification
] | [
	"rgba",
	number | ExpressionSpecification,
	number | ExpressionSpecification,
	number | ExpressionSpecification,
	number | ExpressionSpecification
] | [
	"to-rgba",
	ColorSpecification | ExpressionSpecification
] | [
	"-",
	number | ExpressionSpecification,
	(number | ExpressionSpecification)?
] | [
	"*",
	number | ExpressionSpecification,
	number | ExpressionSpecification,
	...(number | ExpressionSpecification)[]
] | [
	"/",
	number | ExpressionSpecification,
	number | ExpressionSpecification
] | [
	"%",
	number | ExpressionSpecification,
	number | ExpressionSpecification
] | [
	"^",
	number | ExpressionSpecification,
	number | ExpressionSpecification
] | [
	"+",
	...(number | ExpressionSpecification)[]
] | [
	"abs",
	number | ExpressionSpecification
] | [
	"acos",
	number | ExpressionSpecification
] | [
	"asin",
	number | ExpressionSpecification
] | [
	"atan",
	number | ExpressionSpecification
] | [
	"ceil",
	number | ExpressionSpecification
] | [
	"cos",
	number | ExpressionSpecification
] | [
	"distance",
	Record<string, unknown> | ExpressionSpecification
] | [
	"ExpressionSpecification"
] | [
	"floor",
	number | ExpressionSpecification
] | [
	"ln",
	number | ExpressionSpecification
] | [
	"ln2"
] | [
	"log10",
	number | ExpressionSpecification
] | [
	"log2",
	number | ExpressionSpecification
] | [
	"max",
	number | ExpressionSpecification,
	...(number | ExpressionSpecification)[]
] | [
	"min",
	number | ExpressionSpecification,
	...(number | ExpressionSpecification)[]
] | [
	"pi"
] | [
	"round",
	number | ExpressionSpecification
] | [
	"sin",
	number | ExpressionSpecification
] | [
	"sqrt",
	number | ExpressionSpecification
] | [
	"tan",
	number | ExpressionSpecification
] | [
	"zoom"
] | [
	"heatmap-density"
];
export type ExpressionFilterSpecification = boolean | ExpressionSpecification;
export type LegacyFilterSpecification = [
	"has",
	string
] | [
	"!has",
	string
] | [
	"==",
	string,
	string | number | boolean
] | [
	"!=",
	string,
	string | number | boolean
] | [
	">",
	string,
	string | number | boolean
] | [
	">=",
	string,
	string | number | boolean
] | [
	"<",
	string,
	string | number | boolean
] | [
	"<=",
	string,
	string | number | boolean
] | [
	"in",
	string,
	...(string | number | boolean)[]
] | [
	"!in",
	string,
	...(string | number | boolean)[]
] | [
	"all",
	...LegacyFilterSpecification[]
] | [
	"any",
	...LegacyFilterSpecification[]
] | [
	"none",
	...LegacyFilterSpecification[]
];
export type FilterSpecification = ExpressionFilterSpecification | LegacyFilterSpecification;
export type TransitionSpecification = {
	duration?: number;
	delay?: number;
};
export type CameraFunctionSpecification<T> = {
	type: "exponential";
	stops: Array<[
		number,
		T
	]>;
} | {
	type: "interval";
	stops: Array<[
		number,
		T
	]>;
};
export type SourceFunctionSpecification<T> = {
	type: "exponential";
	stops: Array<[
		number,
		T
	]>;
	property: string;
	default?: T;
} | {
	type: "interval";
	stops: Array<[
		number,
		T
	]>;
	property: string;
	default?: T;
} | {
	type: "categorical";
	stops: Array<[
		string | number | boolean,
		T
	]>;
	property: string;
	default?: T;
} | {
	type: "identity";
	property: string;
	default?: T;
};
export type CompositeFunctionSpecification<T> = {
	type: "exponential";
	stops: Array<[
		{
			zoom: number;
			value: number;
		},
		T
	]>;
	property: string;
	default?: T;
} | {
	type: "interval";
	stops: Array<[
		{
			zoom: number;
			value: number;
		},
		T
	]>;
	property: string;
	default?: T;
} | {
	type: "categorical";
	stops: Array<[
		{
			zoom: number;
			value: string | number | boolean;
		},
		T
	]>;
	property: string;
	default?: T;
};
export type PropertyValueSpecification<T> = T | CameraFunctionSpecification<T> | ExpressionSpecification;
export type DataDrivenPropertyValueSpecification<T> = T | CameraFunctionSpecification<T> | SourceFunctionSpecification<T> | CompositeFunctionSpecification<T> | ExpressionSpecification;
export type StyleSpecification = {
	"version": 8;
	"name"?: string;
	"metadata"?: unknown;
	"center"?: Array<number>;
	"zoom"?: number;
	"bearing"?: number;
	"pitch"?: number;
	"light"?: LightSpecification;
	"terrain"?: TerrainSpecification;
	"sources": {
		[_: string]: SourceSpecification;
	};
	"sprite"?: SpriteSpecification;
	"glyphs"?: string;
	"transition"?: TransitionSpecification;
	"layers": Array<LayerSpecification>;
};
export type LightSpecification = {
	"anchor"?: PropertyValueSpecification<"map" | "viewport">;
	"position"?: PropertyValueSpecification<[
		number,
		number,
		number
	]>;
	"color"?: PropertyValueSpecification<ColorSpecification>;
	"intensity"?: PropertyValueSpecification<number>;
};
export type TerrainSpecification = {
	"source": string;
	"exaggeration"?: number;
};
export type VectorSourceSpecification = {
	"type": "vector";
	"url"?: string;
	"tiles"?: Array<string>;
	"bounds"?: [
		number,
		number,
		number,
		number
	];
	"scheme"?: "xyz" | "tms";
	"minzoom"?: number;
	"maxzoom"?: number;
	"attribution"?: string;
	"promoteId"?: PromoteIdSpecification;
	"volatile"?: boolean;
};
export type RasterSourceSpecification = {
	"type": "raster";
	"url"?: string;
	"tiles"?: Array<string>;
	"bounds"?: [
		number,
		number,
		number,
		number
	];
	"minzoom"?: number;
	"maxzoom"?: number;
	"tileSize"?: number;
	"scheme"?: "xyz" | "tms";
	"attribution"?: string;
	"volatile"?: boolean;
};
export type RasterDEMSourceSpecification = {
	"type": "raster-dem";
	"url"?: string;
	"tiles"?: Array<string>;
	"bounds"?: [
		number,
		number,
		number,
		number
	];
	"minzoom"?: number;
	"maxzoom"?: number;
	"tileSize"?: number;
	"attribution"?: string;
	"encoding"?: "terrarium" | "mapbox" | "custom";
	"redFactor"?: number;
	"blueFactor"?: number;
	"greenFactor"?: number;
	"baseShift"?: number;
	"volatile"?: boolean;
};
export type GeoJSONSourceSpecification = {
	"type": "geojson";
	"data": unknown;
	"maxzoom"?: number;
	"attribution"?: string;
	"buffer"?: number;
	"filter"?: unknown;
	"tolerance"?: number;
	"cluster"?: boolean;
	"clusterRadius"?: number;
	"clusterMaxZoom"?: number;
	"clusterMinPoints"?: number;
	"clusterProperties"?: unknown;
	"lineMetrics"?: boolean;
	"generateId"?: boolean;
	"promoteId"?: PromoteIdSpecification;
};
export type VideoSourceSpecification = {
	"type": "video";
	"urls": Array<string>;
	"coordinates": [
		[
			number,
			number
		],
		[
			number,
			number
		],
		[
			number,
			number
		],
		[
			number,
			number
		]
	];
};
export type ImageSourceSpecification = {
	"type": "image";
	"url": string;
	"coordinates": [
		[
			number,
			number
		],
		[
			number,
			number
		],
		[
			number,
			number
		],
		[
			number,
			number
		]
	];
};
export type SourceSpecification = VectorSourceSpecification | RasterSourceSpecification | RasterDEMSourceSpecification | GeoJSONSourceSpecification | VideoSourceSpecification | ImageSourceSpecification;
export type FillLayerSpecification = {
	"id": string;
	"type": "fill";
	"metadata"?: unknown;
	"source": string;
	"source-layer"?: string;
	"minzoom"?: number;
	"maxzoom"?: number;
	"filter"?: FilterSpecification;
	"layout"?: {
		"fill-sort-key"?: DataDrivenPropertyValueSpecification<number>;
		"visibility"?: "visible" | "none";
	};
	"paint"?: {
		"fill-antialias"?: PropertyValueSpecification<boolean>;
		"fill-opacity"?: DataDrivenPropertyValueSpecification<number>;
		"fill-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"fill-outline-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"fill-translate"?: PropertyValueSpecification<[
			number,
			number
		]>;
		"fill-translate-anchor"?: PropertyValueSpecification<"map" | "viewport">;
		"fill-pattern"?: DataDrivenPropertyValueSpecification<ResolvedImageSpecification>;
	};
};
export type LineLayerSpecification = {
	"id": string;
	"type": "line";
	"metadata"?: unknown;
	"source": string;
	"source-layer"?: string;
	"minzoom"?: number;
	"maxzoom"?: number;
	"filter"?: FilterSpecification;
	"layout"?: {
		"line-cap"?: PropertyValueSpecification<"butt" | "round" | "square">;
		"line-join"?: DataDrivenPropertyValueSpecification<"bevel" | "round" | "miter">;
		"line-miter-limit"?: PropertyValueSpecification<number>;
		"line-round-limit"?: PropertyValueSpecification<number>;
		"line-sort-key"?: DataDrivenPropertyValueSpecification<number>;
		"visibility"?: "visible" | "none";
	};
	"paint"?: {
		"line-opacity"?: DataDrivenPropertyValueSpecification<number>;
		"line-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"line-translate"?: PropertyValueSpecification<[
			number,
			number
		]>;
		"line-translate-anchor"?: PropertyValueSpecification<"map" | "viewport">;
		"line-width"?: DataDrivenPropertyValueSpecification<number>;
		"line-gap-width"?: DataDrivenPropertyValueSpecification<number>;
		"line-offset"?: DataDrivenPropertyValueSpecification<number>;
		"line-blur"?: DataDrivenPropertyValueSpecification<number>;
		"line-dasharray"?: PropertyValueSpecification<Array<number>>;
		"line-pattern"?: DataDrivenPropertyValueSpecification<ResolvedImageSpecification>;
		"line-gradient"?: ExpressionSpecification;
	};
};
export type SymbolLayerSpecification = {
	"id": string;
	"type": "symbol";
	"metadata"?: unknown;
	"source": string;
	"source-layer"?: string;
	"minzoom"?: number;
	"maxzoom"?: number;
	"filter"?: FilterSpecification;
	"layout"?: {
		"symbol-placement"?: PropertyValueSpecification<"point" | "line" | "line-center">;
		"symbol-spacing"?: PropertyValueSpecification<number>;
		"symbol-avoid-edges"?: PropertyValueSpecification<boolean>;
		"symbol-sort-key"?: DataDrivenPropertyValueSpecification<number>;
		"symbol-z-order"?: PropertyValueSpecification<"auto" | "viewport-y" | "source">;
		"icon-allow-overlap"?: PropertyValueSpecification<boolean>;
		"icon-overlap"?: PropertyValueSpecification<"never" | "always" | "cooperative">;
		"icon-ignore-placement"?: PropertyValueSpecification<boolean>;
		"icon-optional"?: PropertyValueSpecification<boolean>;
		"icon-rotation-alignment"?: PropertyValueSpecification<"map" | "viewport" | "auto">;
		"icon-size"?: DataDrivenPropertyValueSpecification<number>;
		"icon-text-fit"?: PropertyValueSpecification<"none" | "width" | "height" | "both">;
		"icon-text-fit-padding"?: PropertyValueSpecification<[
			number,
			number,
			number,
			number
		]>;
		"icon-image"?: DataDrivenPropertyValueSpecification<ResolvedImageSpecification>;
		"icon-rotate"?: DataDrivenPropertyValueSpecification<number>;
		"icon-padding"?: DataDrivenPropertyValueSpecification<PaddingSpecification>;
		"icon-keep-upright"?: PropertyValueSpecification<boolean>;
		"icon-offset"?: DataDrivenPropertyValueSpecification<[
			number,
			number
		]>;
		"icon-anchor"?: DataDrivenPropertyValueSpecification<"center" | "left" | "right" | "top" | "bottom" | "top-left" | "top-right" | "bottom-left" | "bottom-right">;
		"icon-pitch-alignment"?: PropertyValueSpecification<"map" | "viewport" | "auto">;
		"text-pitch-alignment"?: PropertyValueSpecification<"map" | "viewport" | "auto">;
		"text-rotation-alignment"?: PropertyValueSpecification<"map" | "viewport" | "viewport-glyph" | "auto">;
		"text-field"?: DataDrivenPropertyValueSpecification<FormattedSpecification>;
		"text-font"?: DataDrivenPropertyValueSpecification<Array<string>>;
		"text-size"?: DataDrivenPropertyValueSpecification<number>;
		"text-max-width"?: DataDrivenPropertyValueSpecification<number>;
		"text-line-height"?: PropertyValueSpecification<number>;
		"text-letter-spacing"?: DataDrivenPropertyValueSpecification<number>;
		"text-justify"?: DataDrivenPropertyValueSpecification<"auto" | "left" | "center" | "right">;
		"text-radial-offset"?: DataDrivenPropertyValueSpecification<number>;
		"text-variable-anchor"?: PropertyValueSpecification<Array<"center" | "left" | "right" | "top" | "bottom" | "top-left" | "top-right" | "bottom-left" | "bottom-right">>;
		"text-variable-anchor-offset"?: DataDrivenPropertyValueSpecification<VariableAnchorOffsetCollectionSpecification>;
		"text-anchor"?: DataDrivenPropertyValueSpecification<"center" | "left" | "right" | "top" | "bottom" | "top-left" | "top-right" | "bottom-left" | "bottom-right">;
		"text-max-angle"?: PropertyValueSpecification<number>;
		"text-writing-mode"?: PropertyValueSpecification<Array<"horizontal" | "vertical">>;
		"text-rotate"?: DataDrivenPropertyValueSpecification<number>;
		"text-padding"?: PropertyValueSpecification<number>;
		"text-keep-upright"?: PropertyValueSpecification<boolean>;
		"text-transform"?: DataDrivenPropertyValueSpecification<"none" | "uppercase" | "lowercase">;
		"text-offset"?: DataDrivenPropertyValueSpecification<[
			number,
			number
		]>;
		"text-allow-overlap"?: PropertyValueSpecification<boolean>;
		"text-overlap"?: PropertyValueSpecification<"never" | "always" | "cooperative">;
		"text-ignore-placement"?: PropertyValueSpecification<boolean>;
		"text-optional"?: PropertyValueSpecification<boolean>;
		"visibility"?: "visible" | "none";
	};
	"paint"?: {
		"icon-opacity"?: DataDrivenPropertyValueSpecification<number>;
		"icon-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"icon-halo-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"icon-halo-width"?: DataDrivenPropertyValueSpecification<number>;
		"icon-halo-blur"?: DataDrivenPropertyValueSpecification<number>;
		"icon-translate"?: PropertyValueSpecification<[
			number,
			number
		]>;
		"icon-translate-anchor"?: PropertyValueSpecification<"map" | "viewport">;
		"text-opacity"?: DataDrivenPropertyValueSpecification<number>;
		"text-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"text-halo-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"text-halo-width"?: DataDrivenPropertyValueSpecification<number>;
		"text-halo-blur"?: DataDrivenPropertyValueSpecification<number>;
		"text-translate"?: PropertyValueSpecification<[
			number,
			number
		]>;
		"text-translate-anchor"?: PropertyValueSpecification<"map" | "viewport">;
	};
};
export type CircleLayerSpecification = {
	"id": string;
	"type": "circle";
	"metadata"?: unknown;
	"source": string;
	"source-layer"?: string;
	"minzoom"?: number;
	"maxzoom"?: number;
	"filter"?: FilterSpecification;
	"layout"?: {
		"circle-sort-key"?: DataDrivenPropertyValueSpecification<number>;
		"visibility"?: "visible" | "none";
	};
	"paint"?: {
		"circle-radius"?: DataDrivenPropertyValueSpecification<number>;
		"circle-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"circle-blur"?: DataDrivenPropertyValueSpecification<number>;
		"circle-opacity"?: DataDrivenPropertyValueSpecification<number>;
		"circle-translate"?: PropertyValueSpecification<[
			number,
			number
		]>;
		"circle-translate-anchor"?: PropertyValueSpecification<"map" | "viewport">;
		"circle-pitch-scale"?: PropertyValueSpecification<"map" | "viewport">;
		"circle-pitch-alignment"?: PropertyValueSpecification<"map" | "viewport">;
		"circle-stroke-width"?: DataDrivenPropertyValueSpecification<number>;
		"circle-stroke-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"circle-stroke-opacity"?: DataDrivenPropertyValueSpecification<number>;
	};
};
export type HeatmapLayerSpecification = {
	"id": string;
	"type": "heatmap";
	"metadata"?: unknown;
	"source": string;
	"source-layer"?: string;
	"minzoom"?: number;
	"maxzoom"?: number;
	"filter"?: FilterSpecification;
	"layout"?: {
		"visibility"?: "visible" | "none";
	};
	"paint"?: {
		"heatmap-radius"?: DataDrivenPropertyValueSpecification<number>;
		"heatmap-weight"?: DataDrivenPropertyValueSpecification<number>;
		"heatmap-intensity"?: PropertyValueSpecification<number>;
		"heatmap-color"?: ExpressionSpecification;
		"heatmap-opacity"?: PropertyValueSpecification<number>;
	};
};
export type FillExtrusionLayerSpecification = {
	"id": string;
	"type": "fill-extrusion";
	"metadata"?: unknown;
	"source": string;
	"source-layer"?: string;
	"minzoom"?: number;
	"maxzoom"?: number;
	"filter"?: FilterSpecification;
	"layout"?: {
		"visibility"?: "visible" | "none";
	};
	"paint"?: {
		"fill-extrusion-opacity"?: PropertyValueSpecification<number>;
		"fill-extrusion-color"?: DataDrivenPropertyValueSpecification<ColorSpecification>;
		"fill-extrusion-translate"?: PropertyValueSpecification<[
			number,
			number
		]>;
		"fill-extrusion-translate-anchor"?: PropertyValueSpecification<"map" | "viewport">;
		"fill-extrusion-pattern"?: DataDrivenPropertyValueSpecification<ResolvedImageSpecification>;
		"fill-extrusion-height"?: DataDrivenPropertyValueSpecification<number>;
		"fill-extrusion-base"?: DataDrivenPropertyValueSpecification<number>;
		"fill-extrusion-vertical-gradient"?: PropertyValueSpecification<boolean>;
	};
};
export type RasterLayerSpecification = {
	"id": string;
	"type": "raster";
	"metadata"?: unknown;
	"source": string;
	"source-layer"?: string;
	"minzoom"?: number;
	"maxzoom"?: number;
	"filter"?: FilterSpecification;
	"layout"?: {
		"visibility"?: "visible" | "none";
	};
	"paint"?: {
		"raster-opacity"?: PropertyValueSpecification<number>;
		"raster-hue-rotate"?: PropertyValueSpecification<number>;
		"raster-brightness-min"?: PropertyValueSpecification<number>;
		"raster-brightness-max"?: PropertyValueSpecification<number>;
		"raster-saturation"?: PropertyValueSpecification<number>;
		"raster-contrast"?: PropertyValueSpecification<number>;
		"raster-resampling"?: PropertyValueSpecification<"linear" | "nearest">;
		"raster-fade-duration"?: PropertyValueSpecification<number>;
	};
};
export type HillshadeLayerSpecification = {
	"id": string;
	"type": "hillshade";
	"metadata"?: unknown;
	"source": string;
	"source-layer"?: string;
	"minzoom"?: number;
	"maxzoom"?: number;
	"filter"?: FilterSpecification;
	"layout"?: {
		"visibility"?: "visible" | "none";
	};
	"paint"?: {
		"hillshade-illumination-direction"?: PropertyValueSpecification<number>;
		"hillshade-illumination-anchor"?: PropertyValueSpecification<"map" | "viewport">;
		"hillshade-exaggeration"?: PropertyValueSpecification<number>;
		"hillshade-shadow-color"?: PropertyValueSpecification<ColorSpecification>;
		"hillshade-highlight-color"?: PropertyValueSpecification<ColorSpecification>;
		"hillshade-accent-color"?: PropertyValueSpecification<ColorSpecification>;
	};
};
export type BackgroundLayerSpecification = {
	"id": string;
	"type": "background";
	"metadata"?: unknown;
	"minzoom"?: number;
	"maxzoom"?: number;
	"layout"?: {
		"visibility"?: "visible" | "none";
	};
	"paint"?: {
		"background-color"?: PropertyValueSpecification<ColorSpecification>;
		"background-pattern"?: PropertyValueSpecification<ResolvedImageSpecification>;
		"background-opacity"?: PropertyValueSpecification<number>;
	};
};
export type LayerSpecification = FillLayerSpecification | LineLayerSpecification | SymbolLayerSpecification | CircleLayerSpecification | HeatmapLayerSpecification | FillExtrusionLayerSpecification | RasterLayerSpecification | HillshadeLayerSpecification | BackgroundLayerSpecification;
/**
 * Utility class to assist managing values for text-variable-anchor-offset property. Create instances from
 * bare arrays using the static method `VariableAnchorOffsetCollection.parse`.
 * @private
 */
export declare class VariableAnchorOffsetCollection {
	/** Series of paired of anchor (string) and offset (point) values */
	values: VariableAnchorOffsetCollectionSpecification;
	constructor(values: VariableAnchorOffsetCollectionSpecification);
	static parse(input?: VariableAnchorOffsetCollectionSpecification | VariableAnchorOffsetCollection): VariableAnchorOffsetCollection | undefined;
	toString(): string;
}
export type NullTypeT = {
	kind: "null";
};
export type NumberTypeT = {
	kind: "number";
};
export type StringTypeT = {
	kind: "string";
};
export type BooleanTypeT = {
	kind: "boolean";
};
export type ColorTypeT = {
	kind: "color";
};
export type ObjectTypeT = {
	kind: "object";
};
export type ValueTypeT = {
	kind: "value";
};
export type ErrorTypeT = {
	kind: "error";
};
export type CollatorTypeT = {
	kind: "collator";
};
export type FormattedTypeT = {
	kind: "formatted";
};
export type PaddingTypeT = {
	kind: "padding";
};
export type ResolvedImageTypeT = {
	kind: "resolvedImage";
};
export type VariableAnchorOffsetCollectionTypeT = {
	kind: "variableAnchorOffsetCollection";
};
export type EvaluationKind = "constant" | "source" | "camera" | "composite";
export type Type = NullTypeT | NumberTypeT | StringTypeT | BooleanTypeT | ColorTypeT | ObjectTypeT | ValueTypeT | ArrayType | ErrorTypeT | CollatorTypeT | FormattedTypeT | PaddingTypeT | ResolvedImageTypeT | VariableAnchorOffsetCollectionTypeT;
export interface ArrayType<T extends Type = Type> {
	kind: "array";
	itemType: T;
	N: number;
}
export declare const NullType: NullTypeT;
export declare const ColorType: ColorTypeT;
export declare const FormattedType: FormattedTypeT;
export declare function toString(type: Type): string;
export type Value = null | string | boolean | number | Color | Collator | Formatted | Padding | ResolvedImage | VariableAnchorOffsetCollection | ReadonlyArray<Value> | {
	readonly [x: string]: Value;
};
export declare function typeOf(value: Value): Type;
export interface Point2D {
	x: number;
	y: number;
}
export interface ICanonicalTileID {
	z: number;
	x: number;
	y: number;
	key: string;
	equals(id: ICanonicalTileID): {};
	url(urls: Array<string>, pixelRatio: number, scheme: string | null): {};
	isChildOf(parent: ICanonicalTileID): {};
	getTilePoint(coord: IMercatorCoordinate): Point2D;
	toString(): {};
}
export interface IMercatorCoordinate {
	x: number;
	y: number;
	toLngLat(): {};
	toAltitude(): {};
	meterInMercatorCoordinateUnits(): {};
}
export interface ILngLat {
	wrap(): {};
	toArray(): {};
	toString(): {};
	distanceTo(lngLat: ILngLat): {};
	convert(input: ILngLatLike): ILngLat;
}
export type ILngLatLike = ILngLat | {
	lng: number;
	lat: number;
} | {
	lon: number;
	lat: number;
} | [
	number,
	number
];
export declare class EvaluationContext {
	globals: GlobalProperties;
	feature: Feature;
	featureState: FeatureState;
	formattedSection: FormattedSection;
	availableImages: Array<string>;
	canonical: ICanonicalTileID;
	_parseColorCache: {
		[_: string]: Color;
	};
	constructor();
	id(): any;
	geometryType(): string;
	geometry(): Point2D[][];
	canonicalID(): ICanonicalTileID;
	properties(): {
		[_: string]: any;
	};
	parseColor(input: string): Color;
}
declare class Scope {
	parent: Scope;
	bindings: {
		[_: string]: Expression;
	};
	constructor(parent?: Scope, bindings?: Array<[
		string,
		Expression
	]>);
	concat(bindings: Array<[
		string,
		Expression
	]>): Scope;
	get(name: string): Expression;
	has(name: string): boolean;
}
declare class ParsingContext {
	registry: ExpressionRegistry;
	path: Array<number>;
	key: string;
	scope: Scope;
	errors: Array<ExpressionParsingError>;
	expectedType: Type;
	/**
	 * Internal delegate to inConstant function to avoid circular dependency to CompoundExpression
	 */
	private _isConstant;
	constructor(registry: ExpressionRegistry, isConstantFunc: (expression: Expression) => boolean, path?: Array<number>, expectedType?: Type | null, scope?: Scope, errors?: Array<ExpressionParsingError>);
	/**
	 * @param expr the JSON expression to parse
	 * @param index the optional argument index if this expression is an argument of a parent expression that's being parsed
	 * @param options
	 * @param options.omitTypeAnnotations set true to omit inferred type annotations.  Caller beware: with this option set, the parsed expression's type will NOT satisfy `expectedType` if it would normally be wrapped in an inferred annotation.
	 * @private
	 */
	parse(expr: unknown, index?: number, expectedType?: Type | null, bindings?: Array<[
		string,
		Expression
	]>, options?: {
		typeAnnotation?: "assert" | "coerce" | "omit";
	}): Expression;
	_parse(expr: unknown, options: {
		typeAnnotation?: "assert" | "coerce" | "omit";
	}): Expression;
	/**
	 * Returns a copy of this context suitable for parsing the subexpression at
	 * index `index`, optionally appending to 'let' binding map.
	 *
	 * Note that `errors` property, intended for collecting errors while
	 * parsing, is copied by reference rather than cloned.
	 * @private
	 */
	concat(index: number, expectedType?: Type | null, bindings?: Array<[
		string,
		Expression
	]>): ParsingContext;
	/**
	 * Push a parsing (or type checking) error into the `this.errors`
	 * @param error The message
	 * @param keys Optionally specify the source of the error at a child
	 * of the current expression at `this.key`.
	 * @private
	 */
	error(error: string, ...keys: Array<number>): void;
	/**
	 * Returns null if `t` is a subtype of `expected`; otherwise returns an
	 * error message and also pushes it to `this.errors`.
	 * @param expected The expected type
	 * @param t The actual type
	 * @returns null if `t` is a subtype of `expected`; otherwise returns an error message
	 */
	checkSubtype(expected: Type, t: Type): string;
}
/**
 * Expression
 */
export interface Expression {
	readonly type: Type;
	evaluate(ctx: EvaluationContext): any;
	eachChild(fn: (a: Expression) => void): void;
	/**
	 * Statically analyze the expression, attempting to enumerate possible outputs. Returns
	 * false if the complete set of outputs is statically undecidable, otherwise true.
	 */
	outputDefined(): boolean;
}
export type ExpressionParser = (args: ReadonlyArray<unknown>, context: ParsingContext) => Expression;
export type ExpressionRegistration = {
	new (...args: any): Expression;
} & {
	readonly parse: ExpressionParser;
};
export type ExpressionRegistry = {
	[_: string]: ExpressionRegistration;
};
/**
 * A type used for returning and propagating errors. The first element of the union
 * represents success and contains a value, and the second represents an error and
 * contains an error value.
 * @private
 */
export type Result<T, E> = {
	result: "success";
	value: T;
} | {
	result: "error";
	value: E;
};
export type Stops = Array<[
	number,
	Expression
]>;
export type InterpolationType = {
	name: "linear";
} | {
	name: "exponential";
	base: number;
} | {
	name: "cubic-bezier";
	controlPoints: [
		number,
		number,
		number,
		number
	];
};
export type InterpolatedValueType = NumberTypeT | ColorTypeT | PaddingTypeT | VariableAnchorOffsetCollectionTypeT | ArrayType<NumberTypeT>;
export declare class Interpolate implements Expression {
	type: InterpolatedValueType;
	operator: "interpolate" | "interpolate-hcl" | "interpolate-lab";
	interpolation: InterpolationType;
	input: Expression;
	labels: Array<number>;
	outputs: Array<Expression>;
	constructor(type: InterpolatedValueType, operator: "interpolate" | "interpolate-hcl" | "interpolate-lab", interpolation: InterpolationType, input: Expression, stops: Stops);
	static interpolationFactor(interpolation: InterpolationType, input: number, lower: number, upper: number): number;
	static parse(args: ReadonlyArray<unknown>, context: ParsingContext): Expression;
	evaluate(ctx: EvaluationContext): any;
	eachChild(fn: (_: Expression) => void): void;
	outputDefined(): boolean;
}
export type Feature = {
	readonly type: 0 | 1 | 2 | 3 | "Unknown" | "Point" | "MultiPoint" | "LineString" | "MultiLineString" | "Polygon" | "MultiPolygon";
	readonly id?: any;
	readonly properties: {
		[_: string]: any;
	};
	readonly patterns?: {
		[_: string]: {
			"min": string;
			"mid": string;
			"max": string;
		};
	};
	readonly geometry?: Array<Array<Point2D>>;
};
export type FeatureState = {
	[_: string]: any;
};
export type GlobalProperties = Readonly<{
	zoom: number;
	heatmapDensity?: number;
	lineProgress?: number;
	isSupportedScript?: (_: string) => boolean;
	accumulated?: Value;
}>;
export declare class StyleExpression {
	expression: Expression;
	_evaluator: EvaluationContext;
	_defaultValue: Value;
	_warningHistory: {
		[key: string]: boolean;
	};
	_enumValues: {
		[_: string]: any;
	};
	constructor(expression: Expression, propertySpec?: StylePropertySpecification | null);
	evaluateWithoutErrorHandling(globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>, formattedSection?: FormattedSection): any;
	evaluate(globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>, formattedSection?: FormattedSection): any;
}
export declare function isExpression(expression: unknown): boolean;
/**
 * Parse and typecheck the given style spec JSON expression.  If
 * options.defaultValue is provided, then the resulting StyleExpression's
 * `evaluate()` method will handle errors by logging a warning (once per
 * message) and returning the default value.  Otherwise, it will throw
 * evaluation errors.
 *
 * @private
 */
export declare function createExpression(expression: unknown, propertySpec?: StylePropertySpecification | null): Result<StyleExpression, Array<ExpressionParsingError>>;
export declare class ZoomConstantExpression<Kind extends EvaluationKind> {
	kind: Kind;
	isStateDependent: boolean;
	_styleExpression: StyleExpression;
	constructor(kind: Kind, expression: StyleExpression);
	evaluateWithoutErrorHandling(globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>, formattedSection?: FormattedSection): any;
	evaluate(globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>, formattedSection?: FormattedSection): any;
}
export declare class ZoomDependentExpression<Kind extends EvaluationKind> {
	kind: Kind;
	zoomStops: Array<number>;
	isStateDependent: boolean;
	_styleExpression: StyleExpression;
	interpolationType: InterpolationType;
	constructor(kind: Kind, expression: StyleExpression, zoomStops: Array<number>, interpolationType?: InterpolationType);
	evaluateWithoutErrorHandling(globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>, formattedSection?: FormattedSection): any;
	evaluate(globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>, formattedSection?: FormattedSection): any;
	interpolationFactor(input: number, lower: number, upper: number): number;
}
export declare function isZoomExpression(expression: any): expression is ZoomConstantExpression<"source"> | ZoomDependentExpression<"source">;
export type ConstantExpression = {
	kind: "constant";
	readonly evaluate: (globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>) => any;
};
export type SourceExpression = {
	kind: "source";
	isStateDependent: boolean;
	readonly evaluate: (globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>, formattedSection?: FormattedSection) => any;
};
export type CameraExpression = {
	kind: "camera";
	readonly evaluate: (globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>) => any;
	readonly interpolationFactor: (input: number, lower: number, upper: number) => number;
	zoomStops: Array<number>;
	interpolationType: InterpolationType;
};
export type CompositeExpression = {
	kind: "composite";
	isStateDependent: boolean;
	readonly evaluate: (globals: GlobalProperties, feature?: Feature, featureState?: FeatureState, canonical?: ICanonicalTileID, availableImages?: Array<string>, formattedSection?: FormattedSection) => any;
	readonly interpolationFactor: (input: number, lower: number, upper: number) => number;
	zoomStops: Array<number>;
	interpolationType: InterpolationType;
};
export type StylePropertyExpression = ConstantExpression | SourceExpression | CameraExpression | CompositeExpression;
export declare function createPropertyExpression(expressionInput: unknown, propertySpec: StylePropertySpecification): Result<StylePropertyExpression, Array<ExpressionParsingError>>;
export declare class StylePropertyFunction<T> {
	_parameters: PropertyValueSpecification<T>;
	_specification: StylePropertySpecification;
	kind: EvaluationKind;
	evaluate: (globals: GlobalProperties, feature?: Feature) => any;
	interpolationFactor: ((input: number, lower: number, upper: number) => number);
	zoomStops: Array<number>;
	constructor(parameters: PropertyValueSpecification<T>, specification: StylePropertySpecification);
	static deserialize<T>(serialized: {
		_parameters: PropertyValueSpecification<T>;
		_specification: StylePropertySpecification;
	}): StylePropertyFunction<T>;
	static serialize<T>(input: StylePropertyFunction<T>): {
		_parameters: PropertyValueSpecification<T>;
		_specification: StylePropertySpecification;
	};
}
export declare function normalizePropertyExpression<T>(value: PropertyValueSpecification<T>, specification: StylePropertySpecification): StylePropertyExpression;
export type FilterExpression = (globalProperties: GlobalProperties, feature: Feature, canonical?: ICanonicalTileID) => boolean;
export type FeatureFilter = {
	filter: FilterExpression;
	needGeometry: boolean;
};
declare function isExpressionFilter(filter: any): filter is ExpressionFilterSpecification;
declare function createFilter(filter: any): FeatureFilter;
export type ExpectedTypes = {
	[_: string]: ExpressionInputType;
};
export function convertFilter(filter: FilterSpecification, expectedTypes?: ExpectedTypes): ExpressionFilterSpecification;
export declare function isFunction(value: any): boolean;
export declare function createFunction(parameters: any, propertySpec: any): {
	kind: string;
	interpolationType: {
		name: string;
	};
	interpolationFactor: any;
	zoomStops: any[];
	evaluate({ zoom }: {
		zoom: any;
	}, properties: any): any;
} | {
	kind: string;
	interpolationType: {
		name: string;
		base: any;
	};
	interpolationFactor: any;
	zoomStops: any;
	evaluate: ({ zoom }: {
		zoom: any;
	}) => any;
} | {
	kind: string;
	evaluate(_: any, feature: any): any;
	interpolationType?: undefined;
	interpolationFactor?: undefined;
	zoomStops?: undefined;
};
export declare function convertFunction(parameters: any, propertySpec: StylePropertySpecification): any;
declare function eachSource(style: StyleSpecification, callback: (_: SourceSpecification) => void): void;
declare function eachLayer(style: StyleSpecification, callback: (_: LayerSpecification) => void): void;
export type PropertyCallback = (a: {
	path: [
		string,
		"paint" | "layout",
		string
	];
	key: string;
	value: PropertyValueSpecification<unknown> | DataDrivenPropertyValueSpecification<unknown>;
	reference: StylePropertySpecification;
	set: (a: PropertyValueSpecification<unknown> | DataDrivenPropertyValueSpecification<unknown>) => void;
}) => void;
declare function eachProperty(style: StyleSpecification, options: {
	paint?: boolean;
	layout?: boolean;
}, callback: PropertyCallback): void;
export declare function supportsPropertyExpression(spec: StylePropertySpecification): boolean;
export type InterpolationColorSpace = "rgb" | "hcl" | "lab";
/**
 * @param interpolationType Interpolation type
 * @returns interpolation fn
 * @deprecated use `interpolate[type]` instead
 */
export declare const interpolateFactory: (interpolationType: "number" | "color" | "array" | "padding" | "variableAnchorOffsetCollection") => typeof array | typeof number | typeof color | typeof padding | typeof variableAnchorOffsetCollection;
declare function number(from: number, to: number, t: number): number;
declare function color(from: Color, to: Color, t: number, spaceKey?: InterpolationColorSpace): Color;
declare function array<T extends number[]>(from: T, to: T, t: number): T;
declare function padding(from: Padding, to: Padding, t: number): Padding;
declare function variableAnchorOffsetCollection(from: VariableAnchorOffsetCollection, to: VariableAnchorOffsetCollection, t: number): VariableAnchorOffsetCollection;
declare const interpolate: {
	number: typeof number;
	color: typeof color;
	array: typeof array;
	padding: typeof padding;
	variableAnchorOffsetCollection: typeof variableAnchorOffsetCollection;
};
export declare const expressions: ExpressionRegistry;
/**
 * Given an array of layers, return an array of arrays of layers where all
 * layers in each group have identical layout-affecting properties. These
 * are the properties that were formerly used by explicit `ref` mechanism
 * for layers: 'type', 'source', 'source-layer', 'minzoom', 'maxzoom',
 * 'filter', and 'layout'.
 *
 * The input is not modified. The output layers are references to the
 * input layers.
 *
 * @private
 * @param {Array<Layer>} layers
 * @param {Object} [cachedKeys] - an object to keep already calculated keys.
 * @returns {Array<Array<Layer>>}
 */
export declare function groupByLayout(layers: any, cachedKeys: any): any[];
export function emptyStyle(): StyleSpecification;
/**
 * Validate a MapLibre style against the style specification. This entrypoint,
 * `maplibre-gl-style-spec/lib/validate_style.min`, is designed to produce as
 * small a browserify bundle as possible by omitting unnecessary functionality
 * and legacy style specifications.
 *
 * @private
 * @param {Object} style The style to be validated.
 * @param {Object} [styleSpec] The style specification to validate against.
 *     If omitted, the latest style spec is used.
 * @returns {Array<ValidationError>}
 * @example
 *   var validate = require('maplibre-gl-style-spec/lib/validate_style.min');
 *   var errors = validate(style);
 */
export declare function validateStyleMin(style: StyleSpecification, styleSpec?: any): any[];
export declare namespace validateStyleMin {
	var source: (...args: any[]) => any[];
	var sprite: (...args: any[]) => any[];
	var glyphs: (...args: any[]) => any[];
	var light: (...args: any[]) => any[];
	var terrain: (...args: any[]) => any[];
	var layer: (...args: any[]) => any[];
	var filter: (...args: any[]) => any[];
	var paintProperty: (...args: any[]) => any[];
	var layoutProperty: (...args: any[]) => any[];
}
export declare class Step implements Expression {
	type: Type;
	input: Expression;
	labels: Array<number>;
	outputs: Array<Expression>;
	constructor(type: Type, input: Expression, stops: Stops);
	static parse(args: ReadonlyArray<unknown>, context: ParsingContext): Expression;
	evaluate(ctx: EvaluationContext): any;
	eachChild(fn: (_: Expression) => void): void;
	outputDefined(): boolean;
}
export type FormattedSectionExpression = {
	content: Expression;
	scale: Expression | null;
	font: Expression | null;
	textColor: Expression | null;
};
export declare class FormatExpression implements Expression {
	type: Type;
	sections: Array<FormattedSectionExpression>;
	constructor(sections: Array<FormattedSectionExpression>);
	static parse(args: ReadonlyArray<unknown>, context: ParsingContext): Expression;
	evaluate(ctx: EvaluationContext): Formatted;
	eachChild(fn: (_: Expression) => void): void;
	outputDefined(): boolean;
}
export declare class Literal implements Expression {
	type: Type;
	value: Value;
	constructor(type: Type, value: Value);
	static parse(args: ReadonlyArray<unknown>, context: ParsingContext): Expression;
	evaluate(): Value;
	eachChild(): void;
	outputDefined(): boolean;
}
export type Varargs = {
	type: Type;
};
export type Signature = Array<Type> | Varargs;
export type Evaluate = (b: EvaluationContext, a: Array<Expression>) => Value;
export type Definition = [
	Type,
	Signature,
	Evaluate
] | {
	type: Type;
	overloads: Array<[
		Signature,
		Evaluate
	]>;
};
export declare class CompoundExpression implements Expression {
	name: string;
	type: Type;
	_evaluate: Evaluate;
	args: Array<Expression>;
	static definitions: {
		[_: string]: Definition;
	};
	constructor(name: string, type: Type, evaluate: Evaluate, args: Array<Expression>);
	evaluate(ctx: EvaluationContext): Value;
	eachChild(fn: (_: Expression) => void): void;
	outputDefined(): boolean;
	static parse(args: ReadonlyArray<unknown>, context: ParsingContext): Expression;
	static register(registry: ExpressionRegistry, definitions: {
		[_: string]: Definition;
	}): void;
}
export type ExpressionType = "data-driven" | "cross-faded" | "cross-faded-data-driven" | "color-ramp" | "data-constant" | "constant";
export type ExpressionParameters = Array<"zoom" | "feature" | "feature-state" | "heatmap-density" | "line-progress">;
export type ExpressionSpecificationDefinition = {
	interpolated: boolean;
	parameters: ExpressionParameters;
};
export type StylePropertySpecification = {
	type: "number";
	"property-type": ExpressionType;
	expression?: ExpressionSpecificationDefinition;
	transition: boolean;
	default?: number;
} | {
	type: "string";
	"property-type": ExpressionType;
	expression?: ExpressionSpecificationDefinition;
	transition: boolean;
	default?: string;
	tokens?: boolean;
} | {
	type: "boolean";
	"property-type": ExpressionType;
	expression?: ExpressionSpecificationDefinition;
	transition: boolean;
	default?: boolean;
} | {
	type: "enum";
	"property-type": ExpressionType;
	expression?: ExpressionSpecificationDefinition;
	values: {
		[_: string]: {};
	};
	transition: boolean;
	default?: string;
} | {
	type: "color";
	"property-type": ExpressionType;
	expression?: ExpressionSpecificationDefinition;
	transition: boolean;
	default?: string;
	overridable: boolean;
} | {
	type: "array";
	value: "number";
	"property-type": ExpressionType;
	expression?: ExpressionSpecificationDefinition;
	length?: number;
	transition: boolean;
	default?: Array<number>;
} | {
	type: "array";
	value: "string";
	"property-type": ExpressionType;
	expression?: ExpressionSpecificationDefinition;
	length?: number;
	transition: boolean;
	default?: Array<string>;
} | {
	type: "padding";
	"property-type": ExpressionType;
	expression?: ExpressionSpecificationDefinition;
	transition: boolean;
	default?: number | Array<number>;
} | {
	type: "variableAnchorOffsetCollection";
	"property-type": ExpressionType;
	expression?: ExpressionSpecificationDefinition;
	transition: boolean;
	default?: VariableAnchorOffsetCollectionSpecification;
};
export declare const v8: any;
export declare const expression: {
	StyleExpression: typeof StyleExpression;
	StylePropertyFunction: typeof StylePropertyFunction;
	ZoomConstantExpression: typeof ZoomConstantExpression;
	ZoomDependentExpression: typeof ZoomDependentExpression;
	createExpression: typeof createExpression;
	createPropertyExpression: typeof createPropertyExpression;
	isExpression: typeof isExpression;
	isExpressionFilter: typeof isExpressionFilter;
	isZoomExpression: typeof isZoomExpression;
	normalizePropertyExpression: typeof normalizePropertyExpression;
};
declare const styleFunction: {
	convertFunction: typeof convertFunction;
	createFunction: typeof createFunction;
	isFunction: typeof isFunction;
};
export declare const visit: {
	eachLayer: typeof eachLayer;
	eachProperty: typeof eachProperty;
	eachSource: typeof eachSource;
};

export {
	_default as latest,
	createFilter as featureFilter,
	diffStyles as diff,
	interpolate as interpolates,
	styleFunction as function,
};

export {};
