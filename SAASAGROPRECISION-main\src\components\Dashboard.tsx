import React from 'react';
import { <PERSON><PERSON><PERSON>3, <PERSON><PERSON>ding<PERSON>p, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Droplets, Thermometer, Wind, Sun } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

const Dashboard: React.FC = () => {
  // Dados simulados para os gráficos
  const ndviData = [
    { month: 'Jan', ndvi: 0.65 },
    { month: 'Fev', ndvi: 0.72 },
    { month: 'Mar', ndvi: 0.78 },
    { month: 'Abr', ndvi: 0.82 },
    { month: 'Mai', ndvi: 0.75 },
    { month: 'Jun', ndvi: 0.68 },
  ];

  const weatherData = [
    { day: 'Seg', temp: 28, humidity: 65 },
    { day: 'Ter', temp: 32, humidity: 58 },
    { day: 'Qua', temp: 29, humidity: 72 },
    { day: 'Qui', temp: 31, humidity: 60 },
    { day: 'Sex', temp: 27, humidity: 78 },
    { day: 'Sáb', temp: 30, humidity: 55 },
    { day: 'Dom', temp: 33, humidity: 52 },
  ];

  const cropDistribution = [
    { name: 'Soja', value: 45, color: '#10b981' },
    { name: 'Milho', value: 30, color: '#f59e0b' },
    { name: 'Algodão', value: 15, color: '#3b82f6' },
    { name: 'Pastagem', value: 10, color: '#8b5cf6' },
  ];

  const stats = [
    {
      title: 'Área Total Monitorada',
      value: '2.450 ha',
      change: '+5%',
      changeType: 'positive',
      icon: Leaf,
      color: 'emerald'
    },
    {
      title: 'NDVI Médio',
      value: '0.75',
      change: '+0.08',
      changeType: 'positive',
      icon: BarChart3,
      color: 'blue'
    },
    {
      title: 'Alertas Ativos',
      value: '3',
      change: '-2',
      changeType: 'negative',
      icon: AlertTriangle,
      color: 'red'
    },
    {
      title: 'Eficiência Hídrica',
      value: '87%',
      change: '+12%',
      changeType: 'positive',
      icon: Droplets,
      color: 'cyan'
    }
  ];

  const alerts = [
    {
      id: 1,
      type: 'warning',
      title: 'Baixa Umidade do Solo',
      description: 'Talhão A2 apresenta umidade abaixo do ideal',
      time: '2 horas atrás'
    },
    {
      id: 2,
      type: 'info',
      title: 'Análise NDVI Concluída',
      description: 'Nova análise disponível para Fazenda São João',
      time: '4 horas atrás'
    },
    {
      id: 3,
      type: 'success',
      title: 'Irrigação Otimizada',
      description: 'Sistema ajustado automaticamente no Talhão B1',
      time: '6 horas atrás'
    }
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Agrícola</h1>
          <p className="text-gray-600">Visão geral do monitoramento das suas propriedades rurais</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                    <div className="flex items-center mt-2">
                      <span className={`text-sm font-medium ${
                        stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                      <span className="text-sm text-gray-500 ml-1">vs mês anterior</span>
                    </div>
                  </div>
                  <div className={`p-3 rounded-lg ${
                    stat.color === 'emerald' ? 'bg-emerald-100' :
                    stat.color === 'blue' ? 'bg-blue-100' :
                    stat.color === 'red' ? 'bg-red-100' : 'bg-cyan-100'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      stat.color === 'emerald' ? 'text-emerald-600' :
                      stat.color === 'blue' ? 'text-blue-600' :
                      stat.color === 'red' ? 'text-red-600' : 'text-cyan-600'
                    }`} />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* NDVI Chart */}
          <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Evolução do NDVI</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={ndviData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[0, 1]} />
                <Tooltip />
                <Line type="monotone" dataKey="ndvi" stroke="#10b981" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* Crop Distribution */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribuição de Cultivos</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={cropDistribution}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {cropDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 space-y-2">
              {cropDistribution.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-2" 
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-sm text-gray-600">{item.name}</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{item.value}%</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Weather Data */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Condições Climáticas</h3>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={weatherData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="temp" fill="#f59e0b" name="Temperatura (°C)" />
                <Bar dataKey="humidity" fill="#3b82f6" name="Umidade (%)" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Recent Alerts */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertas Recentes</h3>
            <div className="space-y-4">
              {alerts.map((alert) => (
                <div key={alert.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    alert.type === 'warning' ? 'bg-yellow-500' :
                    alert.type === 'info' ? 'bg-blue-500' : 'bg-green-500'
                  }`} />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">{alert.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{alert.description}</p>
                    <p className="text-xs text-gray-500 mt-2">{alert.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
