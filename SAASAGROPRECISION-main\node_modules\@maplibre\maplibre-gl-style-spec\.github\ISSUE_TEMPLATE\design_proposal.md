---
name: Design Proposal
about: Make a design proposal
title: ''
labels: ''
assignees: ''

---

# Design Proposal: PUT TITLE HERE

## Motivation

Describe the problem you would like to solve.

## Proposed Change

Describe what you would like to do to solve the problem. This part is the actual design proposal and can be extensive with multiple subsections.

## API Modifications

Outline what modifications you expect on the API due to your change.

## Migration Plan and Compatibility

If your proposal replaces existing functionality, write a migration plan how users can get to the new functionality.

Make a statement about the compatibility of your new functionality with existing functionality.

## Rejected Alternatives

Discuss what alternatives to your proposed change you considered and why you rejected them.
